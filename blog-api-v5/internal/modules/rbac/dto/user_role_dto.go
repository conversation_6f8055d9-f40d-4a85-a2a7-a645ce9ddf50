package dto

import (
	"time"
)

// AssignUserRoleDTO represents the request to assign a role to a user
type AssignUserRoleDTO struct {
	UserID      uint       `json:"user_id" binding:"required" example:"123"`
	RoleID      uint       `json:"role_id" binding:"required" example:"456"`
	ContextType string     `json:"context_type,omitempty" binding:"omitempty,oneof=tenant website global" example:"tenant"`
	ContextID   uint       `json:"context_id,omitempty" example:"789"`
	IsPrimary   bool       `json:"is_primary" example:"false"`
	IsTemporary bool       `json:"is_temporary" example:"false"`
	ValidUntil  *time.Time `json:"valid_until,omitempty" example:"2024-12-31T23:59:59Z"`
}

// AssignMultipleRolesDTO represents the request to assign multiple roles to a user
type AssignMultipleRolesDTO struct {
	RoleIDs []uint `json:"role_ids" binding:"required,min=1" example:"[456,789]"`
}

// AssignTemporaryRoleDTO represents the request to assign a temporary role
type AssignTemporaryRoleDTO struct {
	RoleID     uint      `json:"role_id" binding:"required" example:"456"`
	ValidUntil time.Time `json:"valid_until" binding:"required" example:"2024-12-31T23:59:59Z"`
}

// SetPrimaryRoleDTO represents the request to set a primary role
type SetPrimaryRoleDTO struct {
	RoleID      uint   `json:"role_id" binding:"required" example:"456"`
	ContextType string `json:"context_type" binding:"required,oneof=tenant website global" example:"tenant"`
	ContextID   uint   `json:"context_id" binding:"required" example:"789"`
}

// ExtendRoleValidityDTO represents the request to extend role validity
type ExtendRoleValidityDTO struct {
	ValidUntil time.Time `json:"valid_until" binding:"required" example:"2024-12-31T23:59:59Z"`
}

// UserRoleQueryDTO represents query parameters for user role requests
type UserRoleQueryDTO struct {
	Active      bool   `form:"active" example:"true"`
	ContextType string `form:"context_type" binding:"omitempty,oneof=tenant website global" example:"tenant"`
	ContextID   uint   `form:"context_id" example:"789"`
	Status      string `form:"status" binding:"omitempty,oneof=active inactive suspended revoked" example:"active"`
	UserID      uint   `form:"user_id" example:"123"`
	RoleID      uint   `form:"role_id" example:"456"`
	IsPrimary   *bool  `form:"is_primary" example:"true"`
	IsTemporary *bool  `form:"is_temporary" example:"false"`
	Cursor      string `form:"cursor" example:"eyJpZCI6MTIzfQ=="`
	Limit       int    `form:"limit" binding:"omitempty,min=1,max=100" example:"50"`
}

// UserRoleResponseDTO represents a user role in API responses
type UserRoleResponseDTO struct {
	ID          uint             `json:"id" example:"123"`
	UserID      uint             `json:"user_id" example:"456"`
	RoleID      uint             `json:"role_id" example:"789"`
	ContextType string           `json:"context_type" example:"tenant"`
	ContextID   uint             `json:"context_id" example:"101"`
	Status      string           `json:"status" example:"active"`
	IsPrimary   bool             `json:"is_primary" example:"false"`
	IsTemporary bool             `json:"is_temporary" example:"false"`
	ValidFrom   time.Time        `json:"valid_from" example:"2024-01-01T00:00:00Z"`
	ValidUntil  *time.Time       `json:"valid_until,omitempty" example:"2024-12-31T23:59:59Z"`
	AssignedBy  uint             `json:"assigned_by" example:"789"`
	AssignedAt  time.Time        `json:"assigned_at" example:"2024-01-01T00:00:00Z"`
	CreatedAt   time.Time        `json:"created_at" example:"2024-01-01T00:00:00Z"`
	UpdatedAt   time.Time        `json:"updated_at" example:"2024-01-01T00:00:00Z"`
	Role        *RoleResponseDTO `json:"role,omitempty"`
	User        *UserSummaryDTO  `json:"user,omitempty"`
}

// UserPermissionResponseDTO represents user permissions in API responses
type UserPermissionResponseDTO struct {
	ID          uint   `json:"id" example:"123"`
	Name        string `json:"name" example:"users.create"`
	Description string `json:"description" example:"Create new users"`
	Resource    string `json:"resource" example:"users"`
	Action      string `json:"action" example:"create"`
	Source      string `json:"source" example:"role"`
	SourceID    uint   `json:"source_id" example:"456"`
	SourceName  string `json:"source_name" example:"Admin"`
}

// UserRoleHistoryResponseDTO represents user role history in API responses
type UserRoleHistoryResponseDTO struct {
	ID          uint             `json:"id" example:"123"`
	UserID      uint             `json:"user_id" example:"456"`
	RoleID      uint             `json:"role_id" example:"789"`
	Action      string           `json:"action" example:"assigned"`
	ContextType string           `json:"context_type" example:"tenant"`
	ContextID   uint             `json:"context_id" example:"101"`
	PerformedBy uint             `json:"performed_by" example:"789"`
	PerformedAt time.Time        `json:"performed_at" example:"2024-01-01T00:00:00Z"`
	Details     string           `json:"details,omitempty" example:"Assigned admin role"`
	Role        *RoleResponseDTO `json:"role,omitempty"`
}

// RoleUsageStatsResponseDTO represents role usage statistics
type RoleUsageStatsResponseDTO struct {
	RoleID         uint `json:"role_id" example:"123"`
	TotalUsers     int  `json:"total_users" example:"150"`
	ActiveUsers    int  `json:"active_users" example:"120"`
	InactiveUsers  int  `json:"inactive_users" example:"20"`
	SuspendedUsers int  `json:"suspended_users" example:"10"`
	TemporaryUsers int  `json:"temporary_users" example:"5"`
}

// UserRoleListResponseDTO represents paginated user role list
type UserRoleListResponseDTO struct {
	UserRoles []UserRoleResponseDTO `json:"user_roles"`
}

// UserPermissionListResponseDTO represents user permission list
type UserPermissionListResponseDTO struct {
	Permissions []UserPermissionResponseDTO `json:"permissions"`
}

// UserRoleHistoryListResponseDTO represents paginated user role history
type UserRoleHistoryListResponseDTO struct {
	History []UserRoleHistoryResponseDTO `json:"history"`
}

// MessageResponseDTO represents a simple message response
type MessageResponseDTO struct {
	Message string `json:"message" example:"Operation completed successfully"`
}

// UserSummaryDTO represents basic user information
type UserSummaryDTO struct {
	ID       uint   `json:"id" example:"123"`
	Name     string `json:"name" example:"John Doe"`
	Email    string `json:"email" example:"<EMAIL>"`
	Username string `json:"username,omitempty" example:"johndoe"`
}

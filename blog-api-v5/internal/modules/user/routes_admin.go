package user

import (
	"github.com/gin-gonic/gin"
	playgroundvalidator "github.com/go-playground/validator/v10"
	"blog-api-v5/internal/config"
	"blog-api-v5/internal/middleware"
	authMysql "blog-api-v5/internal/modules/auth/repositories/mysql"
	authServices "blog-api-v5/internal/modules/auth/services"
	"blog-api-v5/internal/modules/user/handlers"
	"blog-api-v5/internal/modules/user/repositories/mysql"
	"blog-api-v5/internal/modules/user/services"
	httpmiddleware "blog-api-v5/pkg/http/middleware"
	"blog-api-v5/pkg/http/response"
	"blog-api-v5/pkg/utils"
	"blog-api-v5/pkg/validator"
	"gorm.io/gorm"
)

// RegisterAdminRoutes registers all Admin (superadmin) user routes
func RegisterAdminRoutes(
	router *gin.RouterGroup,
	db *gorm.DB,
	validator validator.Validator,
	logger utils.Logger,
) {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		logger.WithError(err).Error("Failed to load config")
		return
	}

	// Initialize JWT service dependencies
	tokenBlacklistRepo := authMysql.NewTokenBlacklistRepository(db)
	tenantMembershipRepo := mysql.NewTenantMembershipRepository(db, logger)
	tokenBlacklistService := authServices.NewTokenBlacklistService(tokenBlacklistRepo, nil)

	// Create JWT service
	jwtService, err := authServices.NewJWTService(cfg, tokenBlacklistService, tenantMembershipRepo)
	if err != nil {
		logger.WithError(err).Error("Failed to create JWT service")
		return
	}

	// Initialize repositories
	userRepo := mysql.NewUserRepository(db, logger)
	userProfileRepo := mysql.NewUserProfileRepository(db, logger)
	userPreferencesRepo := mysql.NewUserPreferencesRepository(db, logger)
	userSocialLinksRepo := mysql.NewUserSocialLinksRepository(db, logger)

	// Initialize services
	tenantMembershipService := services.NewTenantMembershipService(tenantMembershipRepo, logger)
	userService := services.NewUserService(userRepo, userProfileRepo, userPreferencesRepo, userSocialLinksRepo, tenantMembershipService, logger)
	userAnalyticsService := services.NewUserAnalyticsService(userRepo, userProfileRepo, logger)

	// Get underlying validator
	var validatorInstance *playgroundvalidator.Validate
	if pv, ok := validator.(interface{ GetUnderlyingValidator() *playgroundvalidator.Validate }); ok {
		validatorInstance = pv.GetUnderlyingValidator()
	} else {
		// Fallback to creating a new validator if type assertion fails
		validatorInstance = playgroundvalidator.New()
	}

	// Initialize admin-specific handlers
	adminUserHandler := handlers.NewAdminUserHandler(userService, tenantMembershipService, validatorInstance, logger)
	adminUserAnalyticsHandler := handlers.NewAdminUserAnalyticsHandler(userAnalyticsService, logger)
	adminTenantMembershipHandler := handlers.NewAdminTenantMembershipHandler(tenantMembershipService, logger)

	// Admin-only routes - these are for global operations that don't require tenant context
	adminOnlyRoutes := router.Group("/admin-operations")
	adminOnlyRoutes.Use(httpmiddleware.JWTAuthMiddleware(jwtService))
	adminOnlyRoutes.Use(httpmiddleware.RequireAuthentication())
	adminOnlyRoutes.Use(middleware.RequireSuperAdmin())
	{
		// Global user operations (cross-tenant)
		adminOnlyRoutes.GET("/users/all", httpmiddleware.RequirePermission("admin.users.list"), adminUserHandler.ListAllUsers)
		adminOnlyRoutes.GET("/users/by-tenant/:tenantId", httpmiddleware.RequirePermission("admin.users.list"), adminUserHandler.ListUsersByTenant)
		adminOnlyRoutes.GET("/users/search", httpmiddleware.RequirePermission("admin.users.list"), adminUserHandler.SearchAllUsers)
		adminOnlyRoutes.GET("/users/stats", httpmiddleware.RequirePermission("admin.users.analytics"), adminUserHandler.GetGlobalUserStats)
		
		// User tenant management
		adminOnlyRoutes.PUT("/users/:id/primary-tenant", httpmiddleware.RequirePermission("admin.users.update"), adminUserHandler.ChangeUserPrimaryTenant)
		adminOnlyRoutes.POST("/users/:id/assign-tenant", httpmiddleware.RequirePermission("admin.users.update"), adminUserHandler.AssignUserToTenant)
		adminOnlyRoutes.DELETE("/users/:id/remove-tenant/:tenantId", httpmiddleware.RequirePermission("admin.users.update"), adminUserHandler.RemoveUserFromTenant)
		adminOnlyRoutes.GET("/users/:id/all-memberships", httpmiddleware.RequirePermission("admin.users.read"), adminUserHandler.GetUserAllTenantMemberships)
		
		// Bulk operations
		adminOnlyRoutes.POST("/users/bulk/export", httpmiddleware.RequirePermission("admin.users.export"), adminUserHandler.BulkExportUsers)
		adminOnlyRoutes.POST("/users/bulk/import", httpmiddleware.RequirePermission("admin.users.import"), adminUserHandler.BulkImportUsers)
		adminOnlyRoutes.DELETE("/users/bulk", httpmiddleware.RequirePermission("admin.users.delete"), adminUserHandler.BulkDeleteUsers)
		
		// System operations
		adminOnlyRoutes.DELETE("/cleanup/unverified-users", httpmiddleware.RequirePermission("admin.system.cleanup"), adminUserHandler.CleanupUnverifiedUsers)
		adminOnlyRoutes.DELETE("/cleanup/inactive-users", httpmiddleware.RequirePermission("admin.system.cleanup"), adminUserHandler.CleanupInactiveUsers)
		adminOnlyRoutes.POST("/cleanup/orphaned-users", httpmiddleware.RequirePermission("admin.system.cleanup"), adminUserHandler.CleanupOrphanedUsers)
		
		// Audit and compliance
		adminOnlyRoutes.GET("/audit/user-changes", httpmiddleware.RequirePermission("admin.system.audit"), adminUserHandler.GetUserChangeAuditLog)
		adminOnlyRoutes.GET("/audit/access-logs", httpmiddleware.RequirePermission("admin.system.audit"), adminUserHandler.GetAccessAuditLog)
		adminOnlyRoutes.POST("/compliance/gdpr-export/:userId", httpmiddleware.RequirePermission("admin.system.compliance"), adminUserHandler.ExportUserDataGDPR)
		adminOnlyRoutes.DELETE("/compliance/gdpr-delete/:userId", httpmiddleware.RequirePermission("admin.system.compliance"), adminUserHandler.DeleteUserDataGDPR)
	}

	// Analytics routes - global analytics across all tenants
	analyticsRoutes := router.Group("/analytics")
	analyticsRoutes.Use(httpmiddleware.JWTAuthMiddleware(jwtService))
	analyticsRoutes.Use(httpmiddleware.RequireAuthentication())
	analyticsRoutes.Use(middleware.RequireSuperAdmin())
	{
		// User analytics
		analyticsRoutes.GET("/users/growth", httpmiddleware.RequirePermission("admin.users.analytics"), adminUserAnalyticsHandler.GetUserGrowthStats)
		analyticsRoutes.GET("/users/retention", httpmiddleware.RequirePermission("admin.users.analytics"), adminUserAnalyticsHandler.GetRetentionMetrics)
		analyticsRoutes.GET("/users/engagement", httpmiddleware.RequirePermission("admin.users.analytics"), adminUserAnalyticsHandler.GetEngagementMetrics)
		analyticsRoutes.GET("/users/demographics", httpmiddleware.RequirePermission("admin.users.analytics"), adminUserAnalyticsHandler.GetDemographicsBreakdown)
		
		// Tenant-specific analytics
		analyticsRoutes.GET("/tenants/:tenantId/users", httpmiddleware.RequirePermission("admin.users.analytics"), adminUserAnalyticsHandler.GetTenantUserStats)
		analyticsRoutes.GET("/tenants/:tenantId/activity", httpmiddleware.RequirePermission("admin.users.analytics"), adminUserAnalyticsHandler.GetTenantActivityStats)
		analyticsRoutes.GET("/tenants/comparison", httpmiddleware.RequirePermission("admin.users.analytics"), adminUserAnalyticsHandler.CompareTenantStats)
		
		// System-wide analytics
		analyticsRoutes.GET("/system/user-distribution", httpmiddleware.RequirePermission("admin.users.analytics"), adminUserAnalyticsHandler.GetUserDistribution)
		analyticsRoutes.GET("/system/inactive-users", httpmiddleware.RequirePermission("admin.users.analytics"), adminUserAnalyticsHandler.GetInactiveUsers)
		analyticsRoutes.GET("/system/security-metrics", httpmiddleware.RequirePermission("admin.users.analytics"), adminUserAnalyticsHandler.GetSecurityMetrics)
	}

	// Tenant management routes - for managing tenant memberships
	tenantManagementRoutes := router.Group("/tenant-management")
	tenantManagementRoutes.Use(httpmiddleware.JWTAuthMiddleware(jwtService))
	tenantManagementRoutes.Use(httpmiddleware.RequireAuthentication())
	tenantManagementRoutes.Use(middleware.RequireSuperAdmin())
	{
		// Tenant member management
		tenantManagementRoutes.GET("/tenants/:tenantId/members", httpmiddleware.RequirePermission("admin.tenant.read"), adminTenantMembershipHandler.GetTenantMembers)
		tenantManagementRoutes.POST("/tenants/:tenantId/members/bulk", httpmiddleware.RequirePermission("admin.tenant.update"), adminTenantMembershipHandler.BulkAddMembers)
		tenantManagementRoutes.DELETE("/tenants/:tenantId/members/bulk", httpmiddleware.RequirePermission("admin.tenant.update"), adminTenantMembershipHandler.BulkRemoveMembers)
		tenantManagementRoutes.POST("/tenants/:tenantId/migrate-users", httpmiddleware.RequirePermission("admin.tenant.update"), adminTenantMembershipHandler.MigrateUsersBetweenTenants)
		
		// Tenant statistics
		tenantManagementRoutes.GET("/tenants/:tenantId/stats", httpmiddleware.RequirePermission("admin.tenant.analytics"), adminTenantMembershipHandler.GetTenantStats)
		tenantManagementRoutes.GET("/tenants/all/stats", httpmiddleware.RequirePermission("admin.tenant.analytics"), adminTenantMembershipHandler.GetAllTenantsStats)
	}

	// Tenant Context Routes - Admin can switch to any tenant context and use CMS routes
	// This allows admins to use existing tenant functionality without code duplication
	// Usage: Set header "X-Admin-Tenant-ID: <tenant_id>" to switch context
	tenantContextInfo := router.Group("/tenant-context")
	tenantContextInfo.Use(httpmiddleware.JWTAuthMiddleware(jwtService))
	tenantContextInfo.Use(httpmiddleware.RequireAuthentication())
	tenantContextInfo.Use(middleware.RequireSuperAdmin())
	{
		// Info endpoint to explain tenant switching
		tenantContextInfo.GET("/info", func(c *gin.Context) {
			data := gin.H{
				"message": "Admin Tenant Context Switching",
				"usage": "Set 'X-Admin-Tenant-ID' header with the tenant ID to switch context",
				"example": "X-Admin-Tenant-ID: 123",
				"note": "After setting this header, you can use all /cms/v1/* endpoints with the selected tenant context",
			}
			response.Success(c.Writer, data)
		})
		
		// List available tenants for switching
		tenantContextInfo.GET("/available-tenants", httpmiddleware.RequirePermission("admin.tenant.list"), func(c *gin.Context) {
			// TODO: Implement listing all available tenants
			data := gin.H{"message": "List of available tenants - not implemented"}
			response.Success(c.Writer, data)
		})
	}
}
package handlers

import (
	"fmt"
	"strconv"

	"github.com/gin-gonic/gin"

	"blog-api-v5/internal/modules/user/dto"
	"blog-api-v5/internal/modules/user/models"
	"blog-api-v5/internal/modules/user/services"
	rbacservices "blog-api-v5/internal/modules/rbac/services"
	httpmiddleware "blog-api-v5/pkg/http/middleware"
	"blog-api-v5/pkg/http/response"
	"blog-api-v5/pkg/pagination"
	"blog-api-v5/pkg/utils"
	"blog-api-v5/pkg/validator"
)

// UserHandler handles user-related HTTP requests
type UserHandler struct {
	userService     services.UserService
	userRoleService rbacservices.UserRoleService
	validator       validator.Validator
	logger          utils.Logger
}

// NewUserHandler creates a new user handler
func NewUserHandler(
	userService services.UserService,
	userRoleService rbacservices.UserRoleService,
	validator validator.Validator,
	logger utils.Logger,
) *UserHandler {
	return &UserHandler{
		userService:     userService,
		userRoleService: userRoleService,
		validator:       validator,
		logger:          logger,
	}
}

// CreateUser creates a new user
// @Summary      Create a new user
// @Description  Create a new user with the provided information
// @Tags         Users
// @Accept       json
// @Produce      json
// @Security     Bearer
// @Param        request body dto.UserCreateRequest true "User data"
// @Success      201 {object} response.Response{data=dto.UserResponse} "User created successfully"
// @Failure      400 {object} response.Response "Invalid request body or validation error"
// @Failure      401 {object} response.Response "Authentication required"
// @Failure      409 {object} response.Response "Email or username already exists"
// @Failure      500 {object} response.Response "Failed to create user"
// @Router       /api/cms/v1/users [post]
func (h *UserHandler) CreateUser(c *gin.Context) {
	var req dto.UserCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Failed to bind user input")
		httpmiddleware.AbortWithError(c, utils.BadRequestError("Invalid input data"))
		return
	}

	// Validate input
	if err := h.validator.Validate(c.Request.Context(), req); err != nil {
		h.logger.WithError(err).Error("Failed to validate user input")
		httpmiddleware.AbortWithError(c, err)
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		h.logger.Error("Tenant ID not found in context")
		httpmiddleware.AbortWithError(c, utils.BadRequestError("Tenant ID is required"))
		return
	}
	
	// DEBUG: Log the tenant ID we received from context
	fmt.Printf("[DEBUG UserHandler.CreateUser] Received tenant_id=%v (type=%T) from gin context\n", tenantID, tenantID)

	// Convert DTO to service input
	serviceInput := h.convertToCreateUserInput(req, tenantID.(uint))

	// Create user
	user, err := h.userService.Create(c.Request.Context(), serviceInput)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create user")
		if err.Error() == "email already exists" || err.Error() == "username already exists" {
			httpmiddleware.AbortWithError(c, utils.ConflictError(err.Error()))
			return
		}
		httpmiddleware.AbortWithError(c, utils.InternalError(err))
		return
	}

	// Convert response to DTO
	responseDTO := h.convertToUserResponseWithTenant(user, tenantID.(uint))
	response.CreatedWithContext(c, responseDTO)
}

// GetUser retrieves a user by ID with tenant context
// @Summary Get a user by ID
// @Description Retrieve a user by their unique identifier with tenant membership information
// @Tags users
// @Accept json
// @Produce json
// @Param id path int true "User ID"
// @Success 200 {object} models.User
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /users/{id} [get]
func (h *UserHandler) GetUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		httpmiddleware.AbortWithError(c, utils.BadRequestError("Invalid user ID"))
		return
	}

	// Get tenant ID from context for authorization
	tenantID, tenantExists := c.Get("tenant_id")
	if !tenantExists {
		h.logger.Error("Tenant ID not found in context")
		httpmiddleware.AbortWithError(c, utils.BadRequestError("Tenant ID is required"))
		return
	}

	user, err := h.userService.GetByID(c.Request.Context(), uint(id))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user")
		if err.Error() == "user not found" {
			httpmiddleware.AbortWithError(c, utils.NotFoundError("User not found"))
			return
		}
		httpmiddleware.AbortWithError(c, utils.InternalError(err))
		return
	}

	// Check if user has membership in the current tenant or if request is from system admin
	hasAccess := false
	if user.TenantMemberships != nil {
		for _, membership := range user.TenantMemberships {
			if membership.TenantID == tenantID.(uint) && membership.IsActive() {
				hasAccess = true
				break
			}
		}
	}

	if !hasAccess {
		h.logger.Error("User not found in current tenant", "user_id", id, "tenant_id", tenantID)
		httpmiddleware.AbortWithError(c, utils.NotFoundError("User not found in current tenant"))
		return
	}

	response.SuccessWithContext(c, user)
}

// UpdateUser updates a user
// @Summary      Update a user
// @Description  Update a user's information with tenant authorization
// @Tags         Users
// @Accept       json
// @Produce      json
// @Security     Bearer
// @Param        id path int true "User ID"
// @Param        request body dto.UserUpdateRequest true "User update data"
// @Success      200 {object} response.Response{data=dto.UserResponse} "User updated successfully"
// @Failure      400 {object} response.Response "Invalid request body or validation error"
// @Failure      401 {object} response.Response "Authentication required"
// @Failure      404 {object} response.Response "User not found"
// @Failure      409 {object} response.Response "Email or username already exists"
// @Failure      500 {object} response.Response "Failed to update user"
// @Router       /api/cms/v1/users/{id} [put]
func (h *UserHandler) UpdateUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		httpmiddleware.AbortWithError(c, utils.BadRequestError("Invalid user ID"))
		return
	}

	// Get tenant ID from context for authorization
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		h.logger.Error("Tenant ID not found in context")
		httpmiddleware.AbortWithError(c, utils.BadRequestError("Tenant ID is required"))
		return
	}

	var req dto.UserUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Failed to bind user input")
		httpmiddleware.AbortWithError(c, utils.BadRequestError("Invalid input data"))
		return
	}

	// Validate input
	if err := h.validator.Validate(c.Request.Context(), req); err != nil {
		h.logger.WithError(err).Error("Failed to validate user input")
		httpmiddleware.AbortWithError(c, err)
		return
	}

	// Check if user exists and has membership in current tenant
	user, err := h.userService.GetByID(c.Request.Context(), uint(id))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user for authorization")
		if err.Error() == "user not found" {
			httpmiddleware.AbortWithError(c, utils.NotFoundError("User not found"))
			return
		}
		httpmiddleware.AbortWithError(c, utils.InternalError(err))
		return
	}

	// Check if user has membership in the current tenant
	hasAccess := false
	if user.TenantMemberships != nil {
		for _, membership := range user.TenantMemberships {
			if membership.TenantID == tenantID.(uint) && membership.IsActive() {
				hasAccess = true
				break
			}
		}
	}

	if !hasAccess {
		h.logger.Error("User not found in current tenant", "user_id", id, "tenant_id", tenantID)
		httpmiddleware.AbortWithError(c, utils.NotFoundError("User not found in current tenant"))
		return
	}

	// Convert DTO to service input
	serviceInput := h.convertToUpdateUserInput(req)

	// Update user
	updatedUser, err := h.userService.Update(c.Request.Context(), uint(id), serviceInput)
	if err != nil {
		h.logger.WithError(err).Error("Failed to update user")
		if err.Error() == "user not found" {
			httpmiddleware.AbortWithError(c, utils.NotFoundError("User not found"))
			return
		}
		if err.Error() == "email already exists" || err.Error() == "username already exists" {
			httpmiddleware.AbortWithError(c, utils.ConflictError(err.Error()))
			return
		}
		httpmiddleware.AbortWithError(c, utils.InternalError(err))
		return
	}

	// Convert response to DTO
	responseDTO := h.convertToUserResponse(updatedUser)
	response.SuccessWithContext(c, responseDTO)
}

// DeleteUser deletes a user
// @Summary Delete a user
// @Description Soft delete a user with tenant authorization
// @Tags users
// @Accept json
// @Produce json
// @Param id path int true "User ID"
// @Success 204 "No Content"
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /users/{id} [delete]
func (h *UserHandler) DeleteUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		httpmiddleware.AbortWithError(c, utils.BadRequestError("Invalid user ID"))
		return
	}

	// Get tenant ID from context for authorization
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		h.logger.Error("Tenant ID not found in context")
		httpmiddleware.AbortWithError(c, utils.BadRequestError("Tenant ID is required"))
		return
	}

	// Check if user exists and has membership in current tenant
	user, err := h.userService.GetByID(c.Request.Context(), uint(id))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user for authorization")
		if err.Error() == "user not found" {
			httpmiddleware.AbortWithError(c, utils.NotFoundError("User not found"))
			return
		}
		httpmiddleware.AbortWithError(c, utils.InternalError(err))
		return
	}

	// Check if user has membership in the current tenant
	hasAccess := false
	if user.TenantMemberships != nil {
		for _, membership := range user.TenantMemberships {
			if membership.TenantID == tenantID.(uint) && membership.IsActive() {
				hasAccess = true
				break
			}
		}
	}

	if !hasAccess {
		h.logger.Error("User not found in current tenant", "user_id", id, "tenant_id", tenantID)
		httpmiddleware.AbortWithError(c, utils.NotFoundError("User not found in current tenant"))
		return
	}

	// Delete user
	if err := h.userService.Delete(c.Request.Context(), uint(id)); err != nil {
		h.logger.WithError(err).Error("Failed to delete user")
		if err.Error() == "user not found" {
			httpmiddleware.AbortWithError(c, utils.NotFoundError("User not found"))
			return
		}
		httpmiddleware.AbortWithError(c, utils.InternalError(err))
		return
	}

	response.SuccessWithContext(c, nil)
}

// ListUsers lists users with pagination and filtering
// @Summary List users
// @Description Retrieve a list of users with pagination and filtering options. Supports both tenant-specific and global listing.
// @Tags users
// @Accept json
// @Produce json
// @Param cursor query string false "Cursor for pagination"
// @Param limit query int false "Limit for pagination" default(20)
// @Param status query string false "Filter by status"
// @Param role query string false "Filter by role"
// @Param email_verified query bool false "Filter by email verification status"
// @Param phone_verified query bool false "Filter by phone verification status"
// @Param two_factor_enabled query bool false "Filter by two-factor authentication status"
// @Param search query string false "Search query"
// @Param global query bool false "Search across all tenants (requires admin privileges)"
// @Success 200 {object} services.UserListResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /users [get]
func (h *UserHandler) ListUsers(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		h.logger.Error("Tenant ID not found in context")
		httpmiddleware.AbortWithError(c, utils.BadRequestError("Tenant ID is required"))
		return
	}

	// Get website ID from context
	websiteID, exists := c.Get("website_id")
	if !exists {
		h.logger.Error("Website ID not found in context")
		httpmiddleware.AbortWithError(c, utils.BadRequestError("Website ID is required"))
		return
	}

	// Parse pagination parameters
	cursor := c.Query("cursor")
	limitStr := c.DefaultQuery("limit", "20")
	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		h.logger.WithError(err).Error("Invalid limit parameter")
		httpmiddleware.AbortWithError(c, utils.BadRequestError("Invalid limit parameter"))
		return
	}

	// Create pagination
	pagination := &pagination.CursorPagination{
		Cursor: cursor,
		Limit:  limit,
	}

	// Parse filter parameters
	filter := services.ListUserFilter{
		Pagination: pagination,
		TenantID:   tenantID.(uint), // Always filter by tenant for security
	}

	// Parse optional filters
	if status := c.Query("status"); status != "" {
		// Convert string to UserStatus enum
		filter.Status = (*models.UserStatus)(&status)
	}

	if role := c.Query("role"); role != "" {
		// Convert string to UserRole enum
		filter.Role = (*models.UserRole)(&role)
	}

	if emailVerified := c.Query("email_verified"); emailVerified != "" {
		verified, err := strconv.ParseBool(emailVerified)
		if err != nil {
			h.logger.WithError(err).Error("Invalid email_verified parameter")
			httpmiddleware.AbortWithError(c, utils.BadRequestError("Invalid email_verified parameter"))
			return
		}
		filter.EmailVerified = &verified
	}

	if phoneVerified := c.Query("phone_verified"); phoneVerified != "" {
		verified, err := strconv.ParseBool(phoneVerified)
		if err != nil {
			h.logger.WithError(err).Error("Invalid phone_verified parameter")
			httpmiddleware.AbortWithError(c, utils.BadRequestError("Invalid phone_verified parameter"))
			return
		}
		filter.PhoneVerified = &verified
	}

	if twoFactorEnabled := c.Query("two_factor_enabled"); twoFactorEnabled != "" {
		enabled, err := strconv.ParseBool(twoFactorEnabled)
		if err != nil {
			h.logger.WithError(err).Error("Invalid two_factor_enabled parameter")
			httpmiddleware.AbortWithError(c, utils.BadRequestError("Invalid two_factor_enabled parameter"))
			return
		}
		filter.TwoFactorEnabled = &enabled
	}

	if search := c.Query("search"); search != "" {
		filter.SearchQuery = search
	}

	// List users
	result, err := h.userService.List(c.Request.Context(), tenantID.(uint), websiteID.(uint), filter)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list users")
		httpmiddleware.AbortWithError(c, utils.InternalError(err))
		return
	}

	// Fetch roles for each user if userRoleService is available
	if h.userRoleService != nil {
		for i, user := range result.Users {
			userRoles, err := h.userRoleService.GetActiveUserRoles(c.Request.Context(), user.ID)
			if err != nil {
				h.logger.WithError(err).WithField("user_id", user.ID).Warn("Failed to fetch user roles")
				// Continue without roles data rather than failing the entire request
				continue
			}
			
			// Convert UserRole models to a simpler format for API response
			if len(userRoles) > 0 {
				roles := make([]interface{}, 0, len(userRoles))
				for _, userRole := range userRoles {
					roleData := map[string]interface{}{
						"id":           userRole.ID,
						"user_id":      userRole.UserID,
						"role_id":      userRole.RoleID,
						"context_type": string(userRole.ContextType),
						"context_id":   userRole.ContextID,
						"status":       string(userRole.Status),
						"is_primary":   userRole.IsPrimary,
						"is_temporary": userRole.IsTemporary,
						"assigned_at":  userRole.AssignedAt,
					}
					roles = append(roles, roleData)
				}
				result.Users[i].Roles = roles
			}
		}
	}

	// Use cursor-based pagination response format according to cursor-rule.md
	response.CursorPaginatedWithContext(c, result.Users, *result.Pagination)
}

// SearchUsers searches users by query
// @Summary Search users
// @Description Search users by query with pagination. Supports both tenant-specific and global search.
// @Tags users
// @Accept json
// @Produce json
// @Param query query string true "Search query"
// @Param cursor query string false "Cursor for pagination"
// @Param limit query int false "Limit for pagination" default(20)
// @Param global query bool false "Search across all tenants (requires admin privileges)"
// @Success 200 {object} services.UserSearchResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /users/search [get]
func (h *UserHandler) SearchUsers(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		h.logger.Error("Tenant ID not found in context")
		httpmiddleware.AbortWithError(c, utils.BadRequestError("Tenant ID is required"))
		return
	}

	// Get query parameter
	query := c.Query("query")
	if query == "" {
		h.logger.Error("Search query is required")
		httpmiddleware.AbortWithError(c, utils.BadRequestError("Search query is required"))
		return
	}

	// Parse pagination parameters
	cursor := c.Query("cursor")
	limitStr := c.DefaultQuery("limit", "20")
	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		h.logger.WithError(err).Error("Invalid limit parameter")
		httpmiddleware.AbortWithError(c, utils.BadRequestError("Invalid limit parameter"))
		return
	}

	// Create pagination
	pagination := &pagination.CursorPagination{
		Cursor: cursor,
		Limit:  limit,
	}

	// Search users with tenant filtering for security
	result, err := h.userService.Search(c.Request.Context(), tenantID.(uint), query, pagination)
	if err != nil {
		h.logger.WithError(err).Error("Failed to search users")
		httpmiddleware.AbortWithError(c, utils.InternalError(err))
		return
	}

	// Use cursor-based pagination response format according to cursor-rule.md
	response.CursorPaginatedWithContext(c, result.Users, *result.Pagination)
}

// UpdateUserStatus updates user status
// @Summary Update user status
// @Description Update the status of a user with tenant authorization
// @Tags users
// @Accept json
// @Produce json
// @Param id path int true "User ID"
// @Param status body map[string]string true "Status data"
// @Success 204 "No Content"
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /users/{id}/status [put]
func (h *UserHandler) UpdateUserStatus(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		httpmiddleware.AbortWithError(c, utils.BadRequestError("Invalid user ID"))
		return
	}

	// Get tenant ID from context for authorization
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		h.logger.Error("Tenant ID not found in context")
		httpmiddleware.AbortWithError(c, utils.BadRequestError("Tenant ID is required"))
		return
	}

	var input struct {
		Status string `json:"status" binding:"required"`
	}
	if err := c.ShouldBindJSON(&input); err != nil {
		h.logger.WithError(err).Error("Failed to bind status input")
		httpmiddleware.AbortWithError(c, utils.BadRequestError("Invalid input data"))
		return
	}

	// Check if user exists and has membership in current tenant
	user, err := h.userService.GetByID(c.Request.Context(), uint(id))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user for authorization")
		if err.Error() == "user not found" {
			httpmiddleware.AbortWithError(c, utils.NotFoundError("User not found"))
			return
		}
		httpmiddleware.AbortWithError(c, utils.InternalError(err))
		return
	}

	// Check if user has membership in the current tenant
	hasAccess := false
	if user.TenantMemberships != nil {
		for _, membership := range user.TenantMemberships {
			if membership.TenantID == tenantID.(uint) && membership.IsActive() {
				hasAccess = true
				break
			}
		}
	}

	if !hasAccess {
		h.logger.Error("User not found in current tenant", "user_id", id, "tenant_id", tenantID)
		httpmiddleware.AbortWithError(c, utils.NotFoundError("User not found in current tenant"))
		return
	}

	// Convert string to UserStatus
	status := models.UserStatus(input.Status)

	// Update user status
	if err := h.userService.UpdateStatus(c.Request.Context(), uint(id), status); err != nil {
		h.logger.WithError(err).Error("Failed to update user status")
		if err.Error() == "user not found" {
			httpmiddleware.AbortWithError(c, utils.NotFoundError("User not found"))
			return
		}
		httpmiddleware.AbortWithError(c, utils.InternalError(err))
		return
	}

	response.SuccessWithContext(c, nil)
}

// UpdateUserPassword updates user password
// @Summary Update user password
// @Description Update a user's password
// @Tags users
// @Accept json
// @Produce json
// @Param id path int true "User ID"
// @Param password body map[string]string true "Password data"
// @Success 204 "No Content"
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /users/{id}/password [put]
func (h *UserHandler) UpdateUserPassword(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		httpmiddleware.AbortWithError(c, utils.BadRequestError("Invalid user ID"))
		return
	}

	// Get tenant ID from context for authorization
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		h.logger.Error("Tenant ID not found in context")
		httpmiddleware.AbortWithError(c, utils.BadRequestError("Tenant ID is required"))
		return
	}

	var input struct {
		Password string `json:"password" binding:"required,min=8"`
	}
	if err := c.ShouldBindJSON(&input); err != nil {
		h.logger.WithError(err).Error("Failed to bind password input")
		httpmiddleware.AbortWithError(c, utils.BadRequestError("Invalid input data"))
		return
	}

	// Check if user exists and has membership in current tenant
	user, err := h.userService.GetByID(c.Request.Context(), uint(id))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user for authorization")
		if err.Error() == "user not found" {
			httpmiddleware.AbortWithError(c, utils.NotFoundError("User not found"))
			return
		}
		httpmiddleware.AbortWithError(c, utils.InternalError(err))
		return
	}

	// Check if user has membership in the current tenant
	hasAccess := false
	if user.TenantMemberships != nil {
		for _, membership := range user.TenantMemberships {
			if membership.TenantID == tenantID.(uint) && membership.IsActive() {
				hasAccess = true
				break
			}
		}
	}

	if !hasAccess {
		h.logger.Error("User not found in current tenant", "user_id", id, "tenant_id", tenantID)
		httpmiddleware.AbortWithError(c, utils.NotFoundError("User not found in current tenant"))
		return
	}

	// Update user password
	if err := h.userService.UpdatePassword(c.Request.Context(), uint(id), input.Password); err != nil {
		h.logger.WithError(err).Error("Failed to update user password")
		if err.Error() == "user not found" {
			httpmiddleware.AbortWithError(c, utils.NotFoundError("User not found"))
			return
		}
		httpmiddleware.AbortWithError(c, utils.InternalError(err))
		return
	}

	response.SuccessWithContext(c, nil)
}

// VerifyUserEmail verifies user email
// @Summary Verify user email
// @Description Verify a user's email address
// @Tags users
// @Accept json
// @Produce json
// @Param id path int true "User ID"
// @Success 204 "No Content"
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /users/{id}/verify-email [post]
func (h *UserHandler) VerifyUserEmail(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		httpmiddleware.AbortWithError(c, utils.BadRequestError("Invalid user ID"))
		return
	}

	// Verify user email
	if err := h.userService.VerifyEmail(c.Request.Context(), uint(id)); err != nil {
		h.logger.WithError(err).Error("Failed to verify user email")
		if err.Error() == "user not found" {
			httpmiddleware.AbortWithError(c, utils.NotFoundError("User not found"))
			return
		}
		httpmiddleware.AbortWithError(c, utils.InternalError(err))
		return
	}

	response.SuccessWithContext(c, nil)
}

// VerifyUserPhone verifies user phone
// @Summary Verify user phone
// @Description Verify a user's phone number
// @Tags users
// @Accept json
// @Produce json
// @Param id path int true "User ID"
// @Success 204 "No Content"
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /users/{id}/verify-phone [post]
func (h *UserHandler) VerifyUserPhone(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		httpmiddleware.AbortWithError(c, utils.BadRequestError("Invalid user ID"))
		return
	}

	// Verify user phone
	if err := h.userService.VerifyPhone(c.Request.Context(), uint(id)); err != nil {
		h.logger.WithError(err).Error("Failed to verify user phone")
		if err.Error() == "user not found" {
			httpmiddleware.AbortWithError(c, utils.NotFoundError("User not found"))
			return
		}
		httpmiddleware.AbortWithError(c, utils.InternalError(err))
		return
	}

	response.SuccessWithContext(c, nil)
}

// EnableTwoFactor enables two-factor authentication
// @Summary Enable two-factor authentication
// @Description Enable two-factor authentication for a user
// @Tags users
// @Accept json
// @Produce json
// @Param id path int true "User ID"
// @Success 200 {object} services.TwoFactorSetup
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /users/{id}/two-factor/enable [post]
func (h *UserHandler) EnableTwoFactor(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		httpmiddleware.AbortWithError(c, utils.BadRequestError("Invalid user ID"))
		return
	}

	// Enable two-factor authentication
	setup, err := h.userService.EnableTwoFactor(c.Request.Context(), uint(id))
	if err != nil {
		h.logger.WithError(err).Error("Failed to enable two-factor authentication")
		if err.Error() == "user not found" {
			httpmiddleware.AbortWithError(c, utils.NotFoundError("User not found"))
			return
		}
		httpmiddleware.AbortWithError(c, utils.InternalError(err))
		return
	}

	response.SuccessWithContext(c, setup)
}

// DisableTwoFactor disables two-factor authentication
// @Summary Disable two-factor authentication
// @Description Disable two-factor authentication for a user
// @Tags users
// @Accept json
// @Produce json
// @Param id path int true "User ID"
// @Success 204 "No Content"
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /users/{id}/two-factor/disable [post]
func (h *UserHandler) DisableTwoFactor(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		httpmiddleware.AbortWithError(c, utils.BadRequestError("Invalid user ID"))
		return
	}

	// Disable two-factor authentication
	if err := h.userService.DisableTwoFactor(c.Request.Context(), uint(id)); err != nil {
		h.logger.WithError(err).Error("Failed to disable two-factor authentication")
		if err.Error() == "user not found" {
			httpmiddleware.AbortWithError(c, utils.NotFoundError("User not found"))
			return
		}
		httpmiddleware.AbortWithError(c, utils.InternalError(err))
		return
	}

	response.SuccessWithContext(c, nil)
}



// Conversion functions

// convertToCreateUserInput converts DTO to service input
func (h *UserHandler) convertToCreateUserInput(req dto.UserCreateRequest, tenantID uint) services.CreateUserInput {
	return services.CreateUserInput{
		TenantID:         tenantID,
		Email:            req.Email,
		Username:         req.Username,
		Password:         req.Password,
		FirstName:        req.FirstName,
		LastName:         req.LastName,
		DisplayName:      req.DisplayName,
		Phone:            req.Phone,
		AvatarURL:        req.AvatarURL,
		Language:         req.Language,
		Timezone:         req.Timezone,
		Role:             req.Role,
		Status:           req.Status,
		SendWelcomeEmail: req.SendWelcomeEmail,
	}
}

// convertToUpdateUserInput converts DTO to service input
func (h *UserHandler) convertToUpdateUserInput(req dto.UserUpdateRequest) services.UpdateUserInput {
	return services.UpdateUserInput{
		Email:       req.Email,
		Username:    req.Username,
		FirstName:   req.FirstName,
		LastName:    req.LastName,
		DisplayName: req.DisplayName,
		Phone:       req.Phone,
		AvatarURL:   req.AvatarURL,
		Language:    req.Language,
		Timezone:    req.Timezone,
		Role:        req.Role,
		Status:      req.Status,
	}
}

// convertToUserResponse converts models.User to DTO response
func (h *UserHandler) convertToUserResponse(user *models.User) *dto.UserResponse {
	response := &dto.UserResponse{
		ID:               user.ID,
		Email:            user.Email,
		Language:         user.Language,
		Timezone:         user.Timezone,
		Status:           user.Status,
		EmailVerified:    user.EmailVerified,
		EmailVerifiedAt:  user.EmailVerifiedAt,
		PhoneVerified:    user.PhoneVerified,
		PhoneVerifiedAt:  user.PhoneVerifiedAt,
		TwoFactorEnabled: user.TwoFactorEnabled,
		LastLoginAt:      user.LastLoginAt,
		LoginCount:       int64(user.LoginCount),
		CreatedAt:        user.CreatedAt,
		UpdatedAt:        user.UpdatedAt,
	}

	// Handle pointer fields
	if user.Username != nil {
		response.Username = *user.Username
	}
	if user.FirstName != nil {
		response.FirstName = *user.FirstName
	}
	if user.LastName != nil {
		response.LastName = *user.LastName
	}
	if user.DisplayName != nil {
		response.DisplayName = *user.DisplayName
	}
	if user.Phone != nil {
		response.Phone = *user.Phone
	}
	if user.AvatarURL != nil {
		response.AvatarURL = *user.AvatarURL
	}

	// Get TenantID from tenant memberships if available
	if user.TenantMemberships != nil && len(user.TenantMemberships) > 0 {
		// Use the first active membership for tenant ID
		for _, membership := range user.TenantMemberships {
			if membership.IsActive() {
				response.TenantID = membership.TenantID
				// Default role since TenantMembership doesn't have role field
				response.Role = models.UserRoleUser
				break
			}
		}
	}

	return response
}

// convertToUserResponseWithTenant converts models.User to DTO response with explicit tenant ID
func (h *UserHandler) convertToUserResponseWithTenant(user *models.User, tenantID uint) *dto.UserResponse {
	// First convert using the existing function
	response := h.convertToUserResponse(user)
	
	// Override the TenantID with the provided one
	response.TenantID = tenantID
	
	return response
}

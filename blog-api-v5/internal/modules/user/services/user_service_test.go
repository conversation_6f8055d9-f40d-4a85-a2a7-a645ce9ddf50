package services

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"

	"blog-api-v5/internal/modules/user/models"
	"blog-api-v5/pkg/pagination"
	"blog-api-v5/pkg/utils"
)

// Mock repositories và services để test
type MockUserRepository struct {
	mock.Mock
}

type MockLogger struct {
	mock.Mock
}

type MockTenantMembershipService struct {
	mock.Mock
}

// Implement các method cần thiết cho UserRepository interface
func (m *MockUserRepository) Create(ctx context.Context, user *models.User) error {
	args := m.Called(ctx, user)
	return args.Error(0)
}

func (m *MockUserRepository) GetByID(ctx context.Context, id uint) (*models.User, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.User), args.Error(1)
}

func (m *MockUserRepository) GetByEmail(ctx context.Context, email string) (*models.User, error) {
	args := m.Called(ctx, email)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.User), args.Error(1)
}

func (m *MockUserRepository) GetByUsername(ctx context.Context, username string) (*models.User, error) {
	args := m.Called(ctx, username)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.User), args.Error(1)
}

func (m *MockUserRepository) Update(ctx context.Context, user *models.User) error {
	args := m.Called(ctx, user)
	return args.Error(0)
}

func (m *MockUserRepository) EmailExists(ctx context.Context, email string) (bool, error) {
	args := m.Called(ctx, email)
	return args.Bool(0), args.Error(1)
}

func (m *MockUserRepository) UsernameExists(ctx context.Context, username string) (bool, error) {
	args := m.Called(ctx, username)
	return args.Bool(0), args.Error(1)
}

func (m *MockUserRepository) EmailExistsExcludingUser(ctx context.Context, email string, userID uint) (bool, error) {
	args := m.Called(ctx, email, userID)
	return args.Bool(0), args.Error(1)
}

func (m *MockUserRepository) UsernameExistsExcludingUser(ctx context.Context, username string, userID uint) (bool, error) {
	args := m.Called(ctx, username, userID)
	return args.Bool(0), args.Error(1)
}

func (m *MockUserRepository) UpdatePassword(ctx context.Context, userID uint, hashedPassword string) error {
	args := m.Called(ctx, userID, hashedPassword)
	return args.Error(0)
}

func (m *MockUserRepository) UpdateEmailVerification(ctx context.Context, userID uint, verified bool) error {
	args := m.Called(ctx, userID, verified)
	return args.Error(0)
}

func (m *MockUserRepository) UpdatePhoneVerification(ctx context.Context, userID uint, verified bool) error {
	args := m.Called(ctx, userID, verified)
	return args.Error(0)
}

func (m *MockUserRepository) UpdateStatus(ctx context.Context, userID uint, status models.UserStatus) error {
	args := m.Called(ctx, userID, status)
	return args.Error(0)
}

func (m *MockUserRepository) SoftDelete(ctx context.Context, id uint) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockUserRepository) List(ctx context.Context, tenantID, websiteID uint, pagination *pagination.CursorPagination) ([]models.User, *pagination.CursorResponse, error) {
	args := m.Called(ctx, tenantID, websiteID, pagination)
	if args.Get(0) == nil {
		return nil, nil, args.Error(2)
	}
	return args.Get(0).([]models.User), args.Get(1).(*pagination.CursorResponse), args.Error(2)
}

func (m *MockUserRepository) Count(ctx context.Context) (int64, error) {
	args := m.Called(ctx)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockUserRepository) Search(ctx context.Context, query string, pagination *pagination.CursorPagination) ([]models.User, *pagination.CursorResponse, error) {
	args := m.Called(ctx, query, pagination)
	if args.Get(0) == nil {
		return nil, nil, args.Error(2)
	}
	return args.Get(0).([]models.User), args.Get(1).(*pagination.CursorResponse), args.Error(2)
}

// Implement Logger interface methods
func (m *MockLogger) Debug(args ...interface{}) {
	m.Called(args...)
}

func (m *MockLogger) Info(args ...interface{}) {
	m.Called(args...)
}

func (m *MockLogger) Error(args ...interface{}) {
	m.Called(args...)
}

func (m *MockLogger) Warn(args ...interface{}) {
	m.Called(args...)
}

func (m *MockLogger) WithError(err error) utils.Logger {
	m.Called(err)
	return m
}

func (m *MockLogger) Debugf(format string, args ...interface{}) {
	allArgs := make([]interface{}, 0, len(args)+1)
	allArgs = append(allArgs, format)
	allArgs = append(allArgs, args...)
	m.Called(allArgs...)
}

func (m *MockLogger) Infof(format string, args ...interface{}) {
	allArgs := make([]interface{}, 0, len(args)+1)
	allArgs = append(allArgs, format)
	allArgs = append(allArgs, args...)
	m.Called(allArgs...)
}

func (m *MockLogger) Errorf(format string, args ...interface{}) {
	allArgs := make([]interface{}, 0, len(args)+1)
	allArgs = append(allArgs, format)
	allArgs = append(allArgs, args...)
	m.Called(allArgs...)
}

func (m *MockLogger) Warnf(format string, args ...interface{}) {
	allArgs := make([]interface{}, 0, len(args)+1)
	allArgs = append(allArgs, format)
	allArgs = append(allArgs, args...)
	m.Called(allArgs...)
}

func (m *MockLogger) Fatal(args ...interface{}) {
	m.Called(args...)
}

func (m *MockLogger) Panic(args ...interface{}) {
	m.Called(args...)
}

func (m *MockLogger) Fatalf(format string, args ...interface{}) {
	allArgs := make([]interface{}, 0, len(args)+1)
	allArgs = append(allArgs, format)
	allArgs = append(allArgs, args...)
	m.Called(allArgs...)
}

func (m *MockLogger) Panicf(format string, args ...interface{}) {
	allArgs := make([]interface{}, 0, len(args)+1)
	allArgs = append(allArgs, format)
	allArgs = append(allArgs, args...)
	m.Called(allArgs...)
}

// Implement TenantMembershipService interface
func (m *MockTenantMembershipService) AddTenantMember(ctx context.Context, input AddTenantMemberInput) (*models.TenantMembership, error) {
	args := m.Called(ctx, input)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.TenantMembership), args.Error(1)
}

// Setup service helper function để khởi tạo service với mock dependencies
func setupUserServiceForTesting() (*userService, *MockUserRepository, *MockTenantMembershipService, *MockLogger) {
	mockUserRepo := new(MockUserRepository)
	mockMembershipService := new(MockTenantMembershipService)
	mockLogger := new(MockLogger)

	service := &userService{
		userRepo:          mockUserRepo,
		profileRepo:       nil, // Simplified for testing
		preferencesRepo:   nil, // Simplified for testing
		socialLinksRepo:   nil, // Simplified for testing
		membershipService: mockMembershipService,
		logger:           mockLogger,
	}

	return service, mockUserRepo, mockMembershipService, mockLogger
}

// Test tạo người dùng mới thành công
func TestCreateUser_Success(t *testing.T) {
	// Setup - Khởi tạo service và mock objects
	service, mockUserRepo, mockMembershipService, mockLogger := setupUserServiceForTesting()
	ctx := context.Background()

	// Test data - Dữ liệu test cho việc tạo user
	input := CreateUserInput{
		Email:            "<EMAIL>",
		Username:         "testuser",
		Password:         "password123",
		FirstName:        "Test",
		LastName:         "User",
		DisplayName:      "Test User",
		Phone:            "+84987654321",
		TenantID:         1,
		Role:             models.UserRoleUser,
		SendWelcomeEmail: true,
		Status:           models.UserStatusActive,
		Language:         "vi",
		Timezone:         "Asia/Ho_Chi_Minh",
	}

	// Mock expectations - Thiết lập kỳ vọng cho mock objects
	mockUserRepo.On("EmailExists", ctx, input.Email).Return(false, nil)
	mockUserRepo.On("UsernameExists", ctx, input.Username).Return(false, nil)
	mockUserRepo.On("Create", ctx, mock.AnythingOfType("*models.User")).Return(nil)
	mockMembershipService.On("AddTenantMember", ctx, mock.AnythingOfType("AddTenantMemberInput")).Return(&models.TenantMembership{}, nil)
	mockLogger.On("Info", mock.Anything, mock.Anything, mock.Anything)

	// Execute - Thực thi method cần test
	result, err := service.Create(ctx, input)

	// Assert - Kiểm tra kết quả
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, input.Email, result.Email)
	assert.Equal(t, input.Username, *result.Username)
	assert.Equal(t, input.FirstName, *result.FirstName)
	assert.Equal(t, input.LastName, *result.LastName)
	assert.Equal(t, input.DisplayName, *result.DisplayName)
	assert.Equal(t, input.Phone, *result.Phone)
	assert.Equal(t, input.Status, result.Status)
	assert.Equal(t, input.Language, result.Language)
	assert.Equal(t, input.Timezone, result.Timezone)
	
	// Verify password was hashed - Kiểm tra mật khẩu đã được hash
	err = bcrypt.CompareHashAndPassword([]byte(result.PasswordHash), []byte(input.Password))
	assert.NoError(t, err)

	// Verify all mocks were called as expected - Xác nhận tất cả mock đã được gọi đúng
	mockUserRepo.AssertExpectations(t)
	mockMembershipService.AssertExpectations(t)
}

// Test tạo người dùng thất bại do email đã tồn tại
func TestCreateUser_FailEmailExists(t *testing.T) {
	// Setup
	service, mockUserRepo, _, _ := setupUserServiceForTesting()
	ctx := context.Background()

	input := CreateUserInput{
		Email:    "<EMAIL>",
		Username: "newuser",
		Password: "password123",
	}

	// Mock email already exists - Giả lập email đã tồn tại
	mockUserRepo.On("EmailExists", ctx, input.Email).Return(true, nil)

	// Execute
	result, err := service.Create(ctx, input)

	// Assert - Kiểm tra lỗi được trả về đúng
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "email already exists")

	mockUserRepo.AssertExpectations(t)
}

// Test tạo người dùng thất bại do username đã tồn tại
func TestCreateUser_FailUsernameExists(t *testing.T) {
	// Setup
	service, mockUserRepo, _, _ := setupUserServiceForTesting()
	ctx := context.Background()

	input := CreateUserInput{
		Email:    "<EMAIL>",
		Username: "existinguser",
		Password: "password123",
	}

	// Mock username already exists - Giả lập username đã tồn tại
	mockUserRepo.On("EmailExists", ctx, input.Email).Return(false, nil)
	mockUserRepo.On("UsernameExists", ctx, input.Username).Return(true, nil)

	// Execute
	result, err := service.Create(ctx, input)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "username already exists")

	mockUserRepo.AssertExpectations(t)
}

// Test tạo người dùng thất bại do mật khẩu không hợp lệ
func TestCreateUser_FailInvalidPassword(t *testing.T) {
	// Setup
	service, mockUserRepo, _, _ := setupUserServiceForTesting()
	ctx := context.Background()

	input := CreateUserInput{
		Email:    "<EMAIL>",
		Username: "newuser",
		Password: "123", // Password quá ngắn
	}

	// Mock validation checks - Giả lập kiểm tra validation
	mockUserRepo.On("EmailExists", ctx, input.Email).Return(false, nil)
	mockUserRepo.On("UsernameExists", ctx, input.Username).Return(false, nil)

	// Execute
	result, err := service.Create(ctx, input)

	// Assert - Kiểm tra lỗi mật khẩu không hợp lệ
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "password must be at least 8 characters long")

	mockUserRepo.AssertExpectations(t)
}

// Test lấy người dùng theo ID thành công
func TestGetByID_Success(t *testing.T) {
	// Setup
	service, mockUserRepo, _, _ := setupUserServiceForTesting()
	ctx := context.Background()
	userID := uint(1)

	// Test data - Dữ liệu user giả lập
	expectedUser := &models.User{
		ID:    userID,
		Email: "<EMAIL>",
	}

	// Mock response - Giả lập phản hồi từ repository
	mockUserRepo.On("GetByID", ctx, userID).Return(expectedUser, nil)

	// Execute
	result, err := service.GetByID(ctx, userID)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, expectedUser.ID, result.ID)
	assert.Equal(t, expectedUser.Email, result.Email)

	mockUserRepo.AssertExpectations(t)
}

// Test lấy người dùng theo ID thất bại - không tìm thấy
func TestGetByID_NotFound(t *testing.T) {
	// Setup
	service, mockUserRepo, _, _ := setupUserServiceForTesting()
	ctx := context.Background()
	userID := uint(999)

	// Mock not found response - Giả lập không tìm thấy user
	mockUserRepo.On("GetByID", ctx, userID).Return(nil, gorm.ErrRecordNotFound)

	// Execute
	result, err := service.GetByID(ctx, userID)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "user not found")

	mockUserRepo.AssertExpectations(t)
}

// Test cập nhật người dùng thành công
func TestUpdateUser_Success(t *testing.T) {
	// Setup
	service, mockUserRepo, _, _ := setupUserServiceForTesting()
	ctx := context.Background()
	userID := uint(1)

	// Current user data - Dữ liệu user hiện tại
	currentUser := &models.User{
		ID:    userID,
		Email: "<EMAIL>",
	}

	// Update data - Dữ liệu cập nhật
	newEmail := "<EMAIL>"
	newFirstName := "New Name"
	updateInput := UpdateUserInput{
		Email:     &newEmail,
		FirstName: &newFirstName,
	}

	// Mock responses - Giả lập các phản hồi
	mockUserRepo.On("GetByID", ctx, userID).Return(currentUser, nil)
	mockUserRepo.On("EmailExistsExcludingUser", ctx, newEmail, userID).Return(false, nil)
	mockUserRepo.On("Update", ctx, mock.AnythingOfType("*models.User")).Return(nil)

	// Execute
	result, err := service.Update(ctx, userID, updateInput)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, newEmail, result.Email)
	assert.Equal(t, newFirstName, *result.FirstName)
	assert.False(t, result.EmailVerified) // Email verification reset khi email thay đổi

	mockUserRepo.AssertExpectations(t)
}

// Test cập nhật mật khẩu thành công
func TestUpdatePassword_Success(t *testing.T) {
	// Setup
	service, mockUserRepo, _, _ := setupUserServiceForTesting()
	ctx := context.Background()
	userID := uint(1)
	newPassword := "newpassword123"

	// Mock response
	mockUserRepo.On("UpdatePassword", ctx, userID, mock.AnythingOfType("string")).Return(nil)

	// Execute
	err := service.UpdatePassword(ctx, userID, newPassword)

	// Assert
	assert.NoError(t, err)
	mockUserRepo.AssertExpectations(t)
}

// Test cập nhật mật khẩu thất bại do mật khẩu không hợp lệ
func TestUpdatePassword_InvalidPassword(t *testing.T) {
	// Setup
	service, _, _, _ := setupUserServiceForTesting()
	ctx := context.Background()
	userID := uint(1)
	invalidPassword := "123" // Quá ngắn

	// Execute
	err := service.UpdatePassword(ctx, userID, invalidPassword)

	// Assert - Kiểm tra lỗi validation mật khẩu
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "password must be at least 8 characters long")
}

// Test xác minh email thành công
func TestVerifyEmail_Success(t *testing.T) {
	// Setup
	service, mockUserRepo, _, _ := setupUserServiceForTesting()
	ctx := context.Background()
	userID := uint(1)

	// Mock response
	mockUserRepo.On("UpdateEmailVerification", ctx, userID, true).Return(nil)

	// Execute
	err := service.VerifyEmail(ctx, userID)

	// Assert
	assert.NoError(t, err)
	mockUserRepo.AssertExpectations(t)
}

// Test xác minh số điện thoại thành công
func TestVerifyPhone_Success(t *testing.T) {
	// Setup
	service, mockUserRepo, _, _ := setupUserServiceForTesting()
	ctx := context.Background()
	userID := uint(1)

	// Mock response
	mockUserRepo.On("UpdatePhoneVerification", ctx, userID, true).Return(nil)

	// Execute
	err := service.VerifyPhone(ctx, userID)

	// Assert
	assert.NoError(t, err)
	mockUserRepo.AssertExpectations(t)
}

// Test cập nhật trạng thái người dùng thành công
func TestUpdateStatus_Success(t *testing.T) {
	// Setup
	service, mockUserRepo, _, _ := setupUserServiceForTesting()
	ctx := context.Background()
	userID := uint(1)
	newStatus := models.UserStatusSuspended

	// Current user data
	currentUser := &models.User{
		ID:     userID,
		Status: models.UserStatusActive,
	}

	// Mock responses
	mockUserRepo.On("GetByID", ctx, userID).Return(currentUser, nil)
	mockUserRepo.On("UpdateStatus", ctx, userID, newStatus).Return(nil)

	// Execute
	err := service.UpdateStatus(ctx, userID, newStatus)

	// Assert
	assert.NoError(t, err)
	mockUserRepo.AssertExpectations(t)
}

// Test xóa mềm người dùng thành công
func TestDeleteUser_Success(t *testing.T) {
	// Setup
	service, mockUserRepo, _, _ := setupUserServiceForTesting()
	ctx := context.Background()
	userID := uint(1)

	// Current user data
	currentUser := &models.User{
		ID:    userID,
		Email: "<EMAIL>",
	}

	// Mock responses
	mockUserRepo.On("GetByID", ctx, userID).Return(currentUser, nil)
	mockUserRepo.On("SoftDelete", ctx, userID).Return(nil)

	// Execute
	err := service.Delete(ctx, userID)

	// Assert
	assert.NoError(t, err)
	mockUserRepo.AssertExpectations(t)
}

// Test lấy danh sách người dùng thành công
func TestListUsers_Success(t *testing.T) {
	// Setup
	service, mockUserRepo, _, _ := setupUserServiceForTesting()
	ctx := context.Background()
	tenantID := uint(1)
	websiteID := uint(1)

	// Test data - Dữ liệu giả lập
	users := []models.User{
		{ID: 1, Email: "<EMAIL>"},
		{ID: 2, Email: "<EMAIL>"},
	}
	paginationResponse := &pagination.CursorResponse{
		NextCursor: "next123",
		HasMore:    false,
	}
	totalCount := int64(2)

	// Filter setup
	filter := ListUserFilter{
		Pagination: &pagination.CursorPagination{
			Limit: 20,
		},
	}

	// Mock responses
	mockUserRepo.On("List", ctx, tenantID, websiteID, filter.Pagination).Return(users, paginationResponse, nil)
	mockUserRepo.On("Count", ctx).Return(totalCount, nil)

	// Execute
	result, err := service.List(ctx, tenantID, websiteID, filter)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Len(t, result.Users, 2)
	assert.Equal(t, totalCount, result.Total)
	assert.Equal(t, paginationResponse, result.Pagination)

	mockUserRepo.AssertExpectations(t)
}

// Test tìm kiếm người dùng thành công
func TestSearchUsers_Success(t *testing.T) {
	// Setup
	service, mockUserRepo, _, _ := setupUserServiceForTesting()
	ctx := context.Background()
	tenantID := uint(1)
	query := "test"

	// Test data
	users := []models.User{
		{ID: 1, Email: "<EMAIL>"},
	}
	paginationResponse := &pagination.CursorResponse{
		NextCursor: "",
		HasMore:    false,
	}
	totalCount := int64(1)
	pag := &pagination.CursorPagination{
		Cursor: "",
		Limit:  20,
	}

	// Mock responses
	mockUserRepo.On("Search", ctx, query, pag).Return(users, paginationResponse, nil)
	mockUserRepo.On("Count", ctx).Return(totalCount, nil)

	// Execute
	result, err := service.Search(ctx, tenantID, query, pag)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Len(t, result.Users, 1)
	assert.Equal(t, totalCount, result.Total)
	assert.Equal(t, query, result.Query)
	assert.Equal(t, paginationResponse, result.Pagination)

	mockUserRepo.AssertExpectations(t)
}

// Test Helper - kiểm tra validate password với các trường hợp khác nhau
func TestValidatePassword_Various_Cases(t *testing.T) {
	service, _, _, _ := setupUserServiceForTesting()

	// Test cases - Các trường hợp test
	testCases := []struct {
		name      string // Tên test case
		password  string // Mật khẩu test
		shouldErr bool   // Có lỗi hay không
		errMsg    string // Thông báo lỗi mong đợi
	}{
		{
			name:      "Valid password", // Mật khẩu hợp lệ
			password:  "password123",
			shouldErr: false,
		},
		{
			name:      "Too short password", // Mật khẩu quá ngắn
			password:  "123",
			shouldErr: true,
			errMsg:    "password must be at least 8 characters long",
		},
		{
			name:      "Empty password", // Mật khẩu rỗng
			password:  "",
			shouldErr: true,
			errMsg:    "password must be at least 8 characters long",
		},
		{
			name:      "Minimum length password", // Mật khẩu đúng độ dài tối thiểu
			password:  "12345678",
			shouldErr: false,
		},
	}

	// Run test cases - Chạy từng test case
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			err := service.validatePassword(tc.password)
			if tc.shouldErr {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tc.errMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Test Helper - kiểm tra hash password
func TestHashPassword_Success(t *testing.T) {
	service, _, _, _ := setupUserServiceForTesting()
	password := "password123"

	// Execute
	hashedPassword, err := service.hashPassword(password)

	// Assert - Kiểm tra password đã được hash đúng cách
	assert.NoError(t, err)
	assert.NotEmpty(t, hashedPassword)
	assert.NotEqual(t, password, hashedPassword)

	// Verify password can be compared - Kiểm tra có thể verify password
	err = bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(password))
	assert.NoError(t, err)
}

// Test GetByEmail thành công
func TestGetByEmail_Success(t *testing.T) {
	// Setup
	service, mockUserRepo, _, _ := setupUserServiceForTesting()
	ctx := context.Background()
	email := "<EMAIL>"
	tenantID := uint(1)

	// Test data
	expectedUser := &models.User{
		ID:    1,
		Email: email,
	}

	// Mock response
	mockUserRepo.On("GetByEmail", ctx, email).Return(expectedUser, nil)

	// Execute
	result, err := service.GetByEmail(ctx, email, tenantID)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, expectedUser.Email, result.Email)

	mockUserRepo.AssertExpectations(t)
}

// Test GetByUsername thành công
func TestGetByUsername_Success(t *testing.T) {
	// Setup
	service, mockUserRepo, _, _ := setupUserServiceForTesting()
	ctx := context.Background()
	username := "testuser"
	tenantID := uint(1)

	// Test data
	expectedUser := &models.User{
		ID:       1,
		Username: &username,
	}

	// Mock response
	mockUserRepo.On("GetByUsername", ctx, username).Return(expectedUser, nil)

	// Execute
	result, err := service.GetByUsername(ctx, username, tenantID)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, username, *result.Username)

	mockUserRepo.AssertExpectations(t)
}
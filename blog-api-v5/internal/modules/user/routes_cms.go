package user

import (
	"github.com/gin-gonic/gin"
	"blog-api-v5/internal/config"
	authMysql "blog-api-v5/internal/modules/auth/repositories/mysql"
	authServices "blog-api-v5/internal/modules/auth/services"
	"blog-api-v5/internal/modules/user/handlers"
	notificationProviders "blog-api-v5/internal/modules/notification/providers"
	notificationRepos "blog-api-v5/internal/modules/notification/repositories"
	notificationServices "blog-api-v5/internal/modules/notification/services"
	rbacmysql "blog-api-v5/internal/modules/rbac/repositories/mysql"
	rbacservices "blog-api-v5/internal/modules/rbac/services"
	"blog-api-v5/internal/modules/user/repositories/mysql"
	"blog-api-v5/internal/modules/user/services"
	httpmiddleware "blog-api-v5/pkg/http/middleware"
	"blog-api-v5/pkg/utils"
	"blog-api-v5/pkg/validator"
	"gorm.io/gorm"
)

// RegisterCMSRoutes registers all CMS (tenant-scoped) user routes
func RegisterCMSRoutes(
	router *gin.RouterGroup,
	db *gorm.DB,
	validator validator.Validator,
	logger utils.Logger,
) {
	logger.Info("RegisterCMSRoutes called for user module")
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		logger.WithError(err).Error("Failed to load config")
		return
	}

	// Initialize JWT service dependencies
	tokenBlacklistRepo := authMysql.NewTokenBlacklistRepository(db)
	tenantMembershipRepo := mysql.NewTenantMembershipRepository(db, logger)
	tokenBlacklistService := authServices.NewTokenBlacklistService(tokenBlacklistRepo, nil)

	// Create JWT service
	jwtService, err := authServices.NewJWTService(cfg, tokenBlacklistService, tenantMembershipRepo)
	if err != nil {
		logger.WithError(err).Error("Failed to create JWT service")
		return
	}

	// Initialize repositories
	userRepo := mysql.NewUserRepository(db, logger)
	userProfileRepo := mysql.NewUserProfileRepository(db, logger)
	userPreferencesRepo := mysql.NewUserPreferencesRepository(db, logger)
	userSocialLinksRepo := mysql.NewUserSocialLinksRepository(db, logger)
	userInvitationRepo := mysql.NewUserInvitationRepository(db, logger)
	phoneVerificationTokenRepo := mysql.NewPhoneVerificationTokenRepository(db)

	// Initialize notification repositories
	notificationRepo := notificationRepos.NewNotificationRepository(db)
	recipientRepo := notificationRepos.NewRecipientRepository(db)
	templateRepo := notificationRepos.NewTemplateRepository(db)
	logRepo := notificationRepos.NewLogRepository(db)

	// Initialize services
	tenantMembershipService := services.NewTenantMembershipService(tenantMembershipRepo, logger)
	userService := services.NewUserService(userRepo, userProfileRepo, userPreferencesRepo, userSocialLinksRepo, tenantMembershipService, logger)
	userProfileService := services.NewUserProfileService(userProfileRepo, userRepo, logger)
	userPreferencesService := services.NewUserPreferencesService(userPreferencesRepo, userRepo, logger)
	userSocialLinksService := services.NewUserSocialLinksService(userSocialLinksRepo, userRepo, logger)
	userSearchService := services.NewUserSearchService(userRepo, userProfileRepo, logger)
	userAnalyticsService := services.NewUserAnalyticsService(userRepo, userProfileRepo, logger)

	// Initialize SMS provider
	smsProvider := notificationProviders.NewTwilioSMSProvider()

	// Initialize notification services
	templateService := notificationServices.NewTemplateService(templateRepo)
	emailProvider := notificationServices.CreateEmailProvider(&cfg.Notification, logger)
	socketProvider := notificationServices.NewMockSocketProvider(logger)
	smsNotificationProvider := notificationServices.NewMockSMSProvider(logger)
	pushProvider := notificationServices.NewMockPushProvider(logger)
	
	deliveryService := notificationServices.NewDeliveryService(
		notificationRepo,
		recipientRepo,
		templateRepo,
		logRepo,
		templateService,
		emailProvider,
		socketProvider,
		smsNotificationProvider,
		pushProvider,
		logger,
	)
	
	notificationService := notificationServices.NewNotificationService(
		notificationRepo,
		recipientRepo,
		templateRepo,
		logRepo,
		deliveryService,
	)

	// Initialize phone verification service
	phoneVerificationService := services.NewPhoneVerificationService(
		phoneVerificationTokenRepo,
		userRepo,
		smsProvider,
		logger,
	)

	// TODO: UserVerificationService needs emailVerificationService and notificationService from auth module
	userVerificationService := services.NewUserVerificationService(userRepo, nil, phoneVerificationService, nil, logger)
	userInvitationService := services.NewUserInvitationService(userInvitationRepo, tenantMembershipRepo, userRepo, notificationService, templateRepo, logger)

	// Initialize RBAC repositories and services for roles functionality
	roleRepo := rbacmysql.NewRoleRepository(db)
	permissionRepo := rbacmysql.NewPermissionRepository(db)
	rolePermissionRepo := rbacmysql.NewRolePermissionRepository(db)
	userRoleRepo := rbacmysql.NewUserRoleRepository(db)
	
	// Initialize RBAC services
	roleService := rbacservices.NewRoleService(roleRepo, rolePermissionRepo, userRoleRepo, permissionRepo)
	userRoleService := rbacservices.NewUserRoleService(userRoleRepo, roleService, nil) // RBACEngine not needed for basic role fetching

	// Initialize handlers
	userHandler := handlers.NewUserHandler(userService, userRoleService, validator, logger)
	userProfileHandler := handlers.NewUserProfileHandler(userProfileService, validator, logger)
	userPreferencesHandler := handlers.NewUserPreferencesHandler(userPreferencesService, validator, logger)
	userSocialLinksHandler := handlers.NewUserSocialLinksHandler(userSocialLinksService, validator, logger)
	userSearchHandler := handlers.NewUserSearchHandler(userSearchService, validator, logger)
	userAnalyticsHandler := handlers.NewUserAnalyticsHandler(userAnalyticsService, validator, logger)
	userVerificationHandler := handlers.NewUserVerificationHandler(userVerificationService, validator, logger)
	phoneVerificationHandler := handlers.NewPhoneVerificationHandler(phoneVerificationService, userService, logger)
	tenantMembershipHandler := handlers.NewTenantMembershipHandler(tenantMembershipService, validator, logger)
	userInvitationHandler := handlers.NewUserInvitationHandler(userInvitationService, validator, logger)

	// CMS User routes - all require authentication and tenant context
	userRoutes := router.Group("/users")
	userRoutes.Use(httpmiddleware.JWTAuthMiddleware(jwtService))
	userRoutes.Use(httpmiddleware.RequireAuthentication())
	userRoutes.Use(httpmiddleware.TenantContextMiddleware())
	{
		userRoutes.POST("", httpmiddleware.RequirePermission("cms.users.create"), userHandler.CreateUser)
		userRoutes.GET("", httpmiddleware.RequirePermission("cms.users.list"), userHandler.ListUsers)
		userRoutes.GET("/search", httpmiddleware.RequirePermission("cms.users.list"), userHandler.SearchUsers)

		userRoutes.GET("/:id", httpmiddleware.RequirePermission("cms.users.read"), userHandler.GetUser)
		userRoutes.PUT("/:id", httpmiddleware.RequirePermission("cms.users.update"), userHandler.UpdateUser)
		userRoutes.DELETE("/:id", httpmiddleware.RequirePermission("cms.users.delete"), userHandler.DeleteUser)
		userRoutes.PUT("/:id/status", httpmiddleware.RequirePermission("cms.users.update"), userHandler.UpdateUserStatus)
		userRoutes.PUT("/:id/password", httpmiddleware.RequirePermission("cms.users.update"), userHandler.UpdateUserPassword)
		userRoutes.POST("/:id/verify-email", httpmiddleware.RequirePermission("cms.users.update"), userHandler.VerifyUserEmail)
		userRoutes.POST("/:id/verify-phone", httpmiddleware.RequirePermission("cms.users.update"), userHandler.VerifyUserPhone)
		userRoutes.POST("/:id/two-factor/enable", httpmiddleware.RequirePermission("cms.users.update"), userHandler.EnableTwoFactor)
		userRoutes.POST("/:id/two-factor/disable", httpmiddleware.RequirePermission("cms.users.update"), userHandler.DisableTwoFactor)

		// Tenant membership routes for users
		userRoutes.GET("/:id/memberships", httpmiddleware.RequirePermission("cms.users.read"), tenantMembershipHandler.GetUserMemberships)
	}

	// CMS User Profile routes - all require authentication and tenant context
	userProfileRoutes := router.Group("/user-profiles")
	userProfileRoutes.Use(httpmiddleware.JWTAuthMiddleware(jwtService))
	userProfileRoutes.Use(httpmiddleware.RequireAuthentication())
	userProfileRoutes.Use(httpmiddleware.TenantContextMiddleware())
	{
		userProfileRoutes.POST("", httpmiddleware.RequirePermission("cms.user-profiles.create"), userProfileHandler.CreateUserProfile)
		userProfileRoutes.POST("/validate", httpmiddleware.RequirePermission("cms.user-profiles.read"), userProfileHandler.ValidateProfile)

		userProfileRoutes.GET("/user/:user_id", httpmiddleware.RequirePermission("cms.user-profiles.read"), userProfileHandler.GetUserProfile)
		userProfileRoutes.PUT("/user/:user_id", httpmiddleware.RequirePermission("cms.user-profiles.update"), userProfileHandler.UpdateUserProfile)
		userProfileRoutes.PUT("/user/:user_id/completion", httpmiddleware.RequirePermission("cms.user-profiles.update"), userProfileHandler.UpdateCompletionStatus)
		userProfileRoutes.PUT("/user/:user_id/custom-fields", httpmiddleware.RequirePermission("cms.user-profiles.update"), userProfileHandler.UpdateCustomFields)
	}

	// CMS User Preferences routes - all require authentication and tenant context
	userPreferencesRoutes := router.Group("/user-preferences")
	userPreferencesRoutes.Use(httpmiddleware.JWTAuthMiddleware(jwtService))
	userPreferencesRoutes.Use(httpmiddleware.RequireAuthentication())
	userPreferencesRoutes.Use(httpmiddleware.TenantContextMiddleware())
	{
		userPreferencesRoutes.POST("", httpmiddleware.RequirePermission("cms.user-profiles.create"), userPreferencesHandler.CreateUserPreferences)
		userPreferencesRoutes.GET("/defaults", userPreferencesHandler.GetDefaultPreferences) // Public info
		userPreferencesRoutes.POST("/validate", httpmiddleware.RequirePermission("cms.user-profiles.read"), userPreferencesHandler.ValidatePreferences)
		userPreferencesRoutes.GET("/stats", httpmiddleware.RequirePermission("cms.users.list"), userPreferencesHandler.GetPreferencesStats)

		userPreferencesRoutes.GET("/user/:user_id", httpmiddleware.RequirePermission("cms.user-profiles.read"), userPreferencesHandler.GetUserPreferences)
		userPreferencesRoutes.PUT("/user/:user_id", httpmiddleware.RequirePermission("cms.user-profiles.update"), userPreferencesHandler.UpdateUserPreferences)
		userPreferencesRoutes.PUT("/user/:user_id/notifications", httpmiddleware.RequirePermission("cms.user-profiles.update"), userPreferencesHandler.UpdateNotificationPreferences)
		userPreferencesRoutes.PUT("/user/:user_id/privacy", httpmiddleware.RequirePermission("cms.user-profiles.update"), userPreferencesHandler.UpdatePrivacyPreferences)
		userPreferencesRoutes.PUT("/user/:user_id/ui", httpmiddleware.RequirePermission("cms.user-profiles.update"), userPreferencesHandler.UpdateUIPreferences)
		userPreferencesRoutes.GET("/user/:user_id/export", httpmiddleware.RequirePermission("cms.user-profiles.read"), userPreferencesHandler.ExportPreferences)
		userPreferencesRoutes.POST("/user/:user_id/import", httpmiddleware.RequirePermission("cms.user-profiles.update"), userPreferencesHandler.ImportPreferences)
	}

	// CMS User Social Links routes - all require authentication and tenant context
	userSocialLinksRoutes := router.Group("/user-social-links")
	userSocialLinksRoutes.Use(httpmiddleware.JWTAuthMiddleware(jwtService))
	userSocialLinksRoutes.Use(httpmiddleware.RequireAuthentication())
	userSocialLinksRoutes.Use(httpmiddleware.TenantContextMiddleware())
	{
		userSocialLinksRoutes.POST("", httpmiddleware.RequirePermission("cms.user-profiles.update"), userSocialLinksHandler.CreateSocialLink)
		userSocialLinksRoutes.POST("/validate", httpmiddleware.RequirePermission("cms.user-profiles.read"), userSocialLinksHandler.ValidateSocialLink)
		userSocialLinksRoutes.GET("/stats", httpmiddleware.RequirePermission("cms.users.list"), userSocialLinksHandler.GetPlatformStats)
		userSocialLinksRoutes.PUT("/bulk/verify", httpmiddleware.RequirePermission("cms.users.update"), userSocialLinksHandler.BulkUpdateVerificationStatus)

		userSocialLinksRoutes.GET("/:id", httpmiddleware.RequirePermission("cms.user-profiles.read"), userSocialLinksHandler.GetSocialLinkByPlatform)
		userSocialLinksRoutes.PUT("/:id", httpmiddleware.RequirePermission("cms.user-profiles.update"), userSocialLinksHandler.UpdateSocialLink)
		userSocialLinksRoutes.DELETE("/:id", httpmiddleware.RequirePermission("cms.user-profiles.update"), userSocialLinksHandler.DeleteSocialLink)
		userSocialLinksRoutes.POST("/:id/verify", httpmiddleware.RequirePermission("cms.users.update"), userSocialLinksHandler.VerifySocialLink)
		userSocialLinksRoutes.POST("/:id/unverify", httpmiddleware.RequirePermission("cms.users.update"), userSocialLinksHandler.UnverifySocialLink)

		userSocialLinksRoutes.GET("/user/:user_id", httpmiddleware.RequirePermission("cms.user-profiles.read"), userSocialLinksHandler.GetUserSocialLinks)
		userSocialLinksRoutes.GET("/user/:user_id/public", userSocialLinksHandler.GetPublicSocialLinks) // Public route
		userSocialLinksRoutes.POST("/user/:user_id/reorder", httpmiddleware.RequirePermission("cms.user-profiles.update"), userSocialLinksHandler.ReorderSocialLinks)
		userSocialLinksRoutes.POST("/user/:user_id/bulk", httpmiddleware.RequirePermission("cms.user-profiles.update"), userSocialLinksHandler.BulkCreateSocialLinks)
		userSocialLinksRoutes.GET("/user/:user_id/platform/:platform", httpmiddleware.RequirePermission("cms.user-profiles.read"), userSocialLinksHandler.GetSocialLinkByPlatform)
	}

	// CMS User Search routes - all require authentication and tenant context
	userSearchRoutes := router.Group("/user-search")
	userSearchRoutes.Use(httpmiddleware.JWTAuthMiddleware(jwtService))
	userSearchRoutes.Use(httpmiddleware.RequireAuthentication())
	userSearchRoutes.Use(httpmiddleware.TenantContextMiddleware())
	{
		userSearchRoutes.GET("", httpmiddleware.RequirePermission("cms.users.list"), userSearchHandler.SearchUsers)
		userSearchRoutes.GET("/skills", httpmiddleware.RequirePermission("cms.users.list"), userSearchHandler.SearchUsersBySkills)
		userSearchRoutes.GET("/location", httpmiddleware.RequirePermission("cms.users.list"), userSearchHandler.SearchUsersByLocation)
		userSearchRoutes.GET("/company", httpmiddleware.RequirePermission("cms.users.list"), userSearchHandler.SearchUsersByCompany)
		userSearchRoutes.GET("/suggestions", httpmiddleware.RequirePermission("cms.users.list"), userSearchHandler.GetSearchSuggestions)
		userSearchRoutes.GET("/popular-skills", httpmiddleware.RequirePermission("cms.users.read"), userSearchHandler.GetPopularSkills)
		userSearchRoutes.GET("/popular-locations", httpmiddleware.RequirePermission("cms.users.read"), userSearchHandler.GetPopularLocations)
	}

	// CMS User Analytics routes - require authentication and tenant context
	userAnalyticsRoutes := router.Group("/user-analytics")
	userAnalyticsRoutes.Use(httpmiddleware.JWTAuthMiddleware(jwtService))
	userAnalyticsRoutes.Use(httpmiddleware.RequireAuthentication())
	userAnalyticsRoutes.Use(httpmiddleware.TenantContextMiddleware())
	{
		userAnalyticsRoutes.GET("/stats", httpmiddleware.RequirePermission("cms.users.list"), userAnalyticsHandler.GetUserStats)
		userAnalyticsRoutes.GET("/registrations", httpmiddleware.RequirePermission("cms.users.list"), userAnalyticsHandler.GetRegistrationStats)
		userAnalyticsRoutes.GET("/engagement", httpmiddleware.RequirePermission("cms.users.list"), userAnalyticsHandler.GetEngagementStats)
		userAnalyticsRoutes.GET("/retention", httpmiddleware.RequirePermission("cms.users.list"), userAnalyticsHandler.GetRetentionStats)
		userAnalyticsRoutes.GET("/demographics", httpmiddleware.RequirePermission("cms.users.list"), userAnalyticsHandler.GetDemographicStats)
		userAnalyticsRoutes.GET("/activity/:user_id", httpmiddleware.RequirePermission("cms.users.read"), userAnalyticsHandler.GetUserActivity)
	}

	// CMS User Verification routes - require authentication and tenant context
	userVerificationRoutes := router.Group("/user-verification")
	userVerificationRoutes.Use(httpmiddleware.JWTAuthMiddleware(jwtService))
	userVerificationRoutes.Use(httpmiddleware.RequireAuthentication())
	userVerificationRoutes.Use(httpmiddleware.TenantContextMiddleware())
	{
		userVerificationRoutes.POST("/:user_id/email/send", httpmiddleware.RequirePermission("cms.users.update"), userVerificationHandler.SendEmailVerification)
		userVerificationRoutes.POST("/:user_id/email/verify", httpmiddleware.RequirePermission("cms.users.update"), userVerificationHandler.VerifyEmail)
		userVerificationRoutes.POST("/:user_id/phone/send", httpmiddleware.RequirePermission("cms.users.update"), userVerificationHandler.SendPhoneVerification)
		userVerificationRoutes.POST("/:user_id/phone/verify", httpmiddleware.RequirePermission("cms.users.update"), userVerificationHandler.VerifyPhone)
		userVerificationRoutes.POST("/:user_id/resend", httpmiddleware.RequirePermission("cms.users.update"), userVerificationHandler.ResendVerification)
		userVerificationRoutes.GET("/:user_id/status", httpmiddleware.RequirePermission("cms.users.read"), userVerificationHandler.CheckVerificationStatus)
	}

	// CMS Phone Verification routes - require authentication and tenant context
	phoneVerificationRoutes := router.Group("/user/phone")
	phoneVerificationRoutes.Use(httpmiddleware.JWTAuthMiddleware(jwtService))
	phoneVerificationRoutes.Use(httpmiddleware.RequireAuthentication())
	phoneVerificationRoutes.Use(httpmiddleware.TenantContextMiddleware())
	{
		phoneVerificationRoutes.POST("/send-verification", httpmiddleware.RequirePermission("cms.users.update"), phoneVerificationHandler.SendPhoneVerification)
		phoneVerificationRoutes.POST("/verify", httpmiddleware.RequirePermission("cms.users.update"), phoneVerificationHandler.VerifyPhone)
		phoneVerificationRoutes.POST("/resend-verification", httpmiddleware.RequirePermission("cms.users.update"), phoneVerificationHandler.ResendPhoneVerification)
		phoneVerificationRoutes.GET("/verification-status/:userId", httpmiddleware.RequirePermission("cms.users.read"), phoneVerificationHandler.GetPhoneVerificationStatus)
		phoneVerificationRoutes.GET("/verification-status", httpmiddleware.RequirePermission("cms.users.read"), phoneVerificationHandler.GetPhoneVerificationStatus)
		phoneVerificationRoutes.GET("/token-stats/:userId", httpmiddleware.RequirePermission("cms.users.read"), phoneVerificationHandler.GetPhoneTokenStats)
		phoneVerificationRoutes.GET("/token-stats", httpmiddleware.RequirePermission("cms.users.read"), phoneVerificationHandler.GetPhoneTokenStats)
	}

	// CMS User Invitation routes - require authentication and tenant context
	invitationRoutes := router.Group("/user-invitations")
	invitationRoutes.Use(httpmiddleware.JWTAuthMiddleware(jwtService))
	invitationRoutes.Use(httpmiddleware.RequireAuthentication())
	invitationRoutes.Use(httpmiddleware.TenantContextMiddleware())
	{
		// Basic CRUD operations
		invitationRoutes.POST("", httpmiddleware.RequirePermission("users.invite"), userInvitationHandler.CreateInvitation)
		invitationRoutes.GET("/:id", httpmiddleware.RequirePermission("users.read"), userInvitationHandler.GetInvitation)
		invitationRoutes.PUT("/:id", httpmiddleware.RequirePermission("users.invite"), userInvitationHandler.UpdateInvitation)
		invitationRoutes.DELETE("/:id", httpmiddleware.RequirePermission("users.invite"), userInvitationHandler.DeleteInvitation)
		invitationRoutes.GET("", httpmiddleware.RequirePermission("users.invite"), userInvitationHandler.ListInvitations)

		// Token-based operations
		invitationRoutes.GET("/token/:token", userInvitationHandler.GetInvitationByToken) // Public - anyone with token can view

		// Invitation actions
		// Accept and reject moved to public routes since they don't require authentication
		invitationRoutes.POST("/:id/revoke", httpmiddleware.RequirePermission("users.invite"), userInvitationHandler.RevokeInvitation)
		invitationRoutes.POST("/:id/resend", httpmiddleware.RequirePermission("users.invite"), userInvitationHandler.ResendInvitation)
	}

	// CMS Tenant Membership routes - require authentication and tenant context
	tenantRoutes := router.Group("/tenants")
	tenantRoutes.Use(httpmiddleware.JWTAuthMiddleware(jwtService))
	tenantRoutes.Use(httpmiddleware.RequireAuthentication())
	tenantRoutes.Use(httpmiddleware.TenantContextMiddleware())
	{
		tenantRoutes.GET("/:id/members", httpmiddleware.RequirePermission("cms.tenant.read"), tenantMembershipHandler.GetTenantMembers)
		tenantRoutes.POST("/:id/members", httpmiddleware.RequirePermission("cms.users.create"), tenantMembershipHandler.AddTenantMember)
		tenantRoutes.GET("/:id/members/stats", httpmiddleware.RequirePermission("cms.tenant.stats.read"), tenantMembershipHandler.GetMembershipStats)

		tenantRoutes.GET("/:id/members/:userId", httpmiddleware.RequirePermission("cms.users.read"), tenantMembershipHandler.GetMembership)
		tenantRoutes.PUT("/:id/members/:userId/role", httpmiddleware.RequirePermission("cms.rbac.users.roles.assign"), tenantMembershipHandler.UpdateMemberRole)
		tenantRoutes.PUT("/:id/members/:userId/status", httpmiddleware.RequirePermission("cms.users.update"), tenantMembershipHandler.UpdateMembershipStatus)
		tenantRoutes.DELETE("/:id/members/:userId", httpmiddleware.RequirePermission("cms.users.delete"), tenantMembershipHandler.RemoveTenantMember)
	}
}
import {
  ArrowLeftOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  EditOutlined,
  HistoryOutlined,
  LockOutlined,
  MailOutlined,
  PhoneOutlined,
  SafetyOutlined,
  UserOutlined,
  GlobalOutlined,
  ClockCircleOutlined,
  LoginOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import {
  Avatar,
  Badge,
  Button,
  Card,
  Col,
  Descriptions,
  Divider,
  Row,
  Space,
  Statistic,
  Tag,
  Timeline,
  Typography,
  Tabs,
  Table,
  Alert,
  message,
} from 'antd';
import dayjs from 'dayjs';
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import { ListHeader } from '../../../components/list-header/list-header';
import { getUser } from '../api';
import { MODULE } from '../config';
import { UserResponse, UserStatus } from '../type';
import UserActivityLogs from '../components/UserActivityLogs';
import UserRoleManager from '../components/UserRoleManager';

const { Title, Text } = Typography;

interface UserSession {
  id: string;
  device: string;
  browser: string;
  ip_address: string;
  location: string;
  last_active: string;
  is_current: boolean;
}

interface UserStats {
  total_logins: number;
  posts_created: number;
  posts_published: number;
  comments_made: number;
  last_login: string;
  account_created: string;
}

export default function UserDetail() {
  const { t } = useTranslation(MODULE);
  const navigate = useNavigateTenant();
  const { id } = useParams<{ id: string }>();
  const logger = ConsoleService.register(MODULE);

  const [loading, setLoading] = useState(false);
  const [user, setUser] = useState<UserResponse | null>(null);
  const [showActivityLogs, setShowActivityLogs] = useState(false);
  const [showRoleManager, setShowRoleManager] = useState(false);
  const [userStats, setUserStats] = useState<UserStats | null>(null);
  const [userSessions, setUserSessions] = useState<UserSession[]>([]);

  useEffect(() => {
    if (id) {
      fetchUserDetail(id);
      fetchUserStats(id);
      fetchUserSessions(id);
    }
  }, [id]);

  const fetchUserDetail = async (userId: string) => {
    setLoading(true);
    try {
      const response = await getUser(userId);
      if (response.status.success) {
        setUser(response.data);
      } else {
        message.error(response.status.message);
      }
    } catch (error: any) {
      message.error('Không thể tải thông tin người dùng');
      logger('Error fetching user:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchUserStats = async (userId: string) => {
    try {
      // TODO: Implement API call
      // Mock data for demonstration
      const mockStats: UserStats = {
        total_logins: 147,
        posts_created: 23,
        posts_published: 18,
        comments_made: 45,
        last_login: dayjs().subtract(2, 'hours').toISOString(),
        account_created: dayjs().subtract(6, 'months').toISOString(),
      };
      setUserStats(mockStats);
    } catch (error) {
      logger('Error fetching user stats:', error);
    }
  };

  const fetchUserSessions = async (userId: string) => {
    try {
      // TODO: Implement API call
      // Mock data for demonstration
      const mockSessions: UserSession[] = [
        {
          id: '1',
          device: 'Windows 10',
          browser: 'Chrome 120.0',
          ip_address: '***********',
          location: 'Ho Chi Minh City, Vietnam',
          last_active: dayjs().subtract(1, 'hour').toISOString(),
          is_current: true,
        },
        {
          id: '2',
          device: 'iPhone 15',
          browser: 'Safari Mobile',
          ip_address: '***********',
          location: 'Ho Chi Minh City, Vietnam',
          last_active: dayjs().subtract(1, 'day').toISOString(),
          is_current: false,
        },
      ];
      setUserSessions(mockSessions);
    } catch (error) {
      logger('Error fetching user sessions:', error);
    }
  };

  const handleResendVerification = async () => {
    try {
      // TODO: Implement API call
      message.success('Đã gửi lại email xác thực');
    } catch (error) {
      message.error('Có lỗi xảy ra khi gửi email xác thực');
    }
  };

  const handleResetPassword = async () => {
    try {
      // TODO: Implement API call
      message.success('Đã gửi email reset mật khẩu đến người dùng');
    } catch (error) {
      message.error('Có lỗi xảy ra khi reset mật khẩu');
    }
  };

  const handleRevokeSession = async (sessionId: string) => {
    try {
      // TODO: Implement API call
      message.success('Đã thu hồi phiên đăng nhập');
      fetchUserSessions(id!);
    } catch (error) {
      message.error('Có lỗi xảy ra khi thu hồi phiên đăng nhập');
    }
  };

  const handleToggleStatus = async () => {
    if (!user) return;

    try {
      const newStatus =
        user.status === UserStatus.ACTIVE
          ? UserStatus.INACTIVE
          : UserStatus.ACTIVE;
      // TODO: Implement API call
      message.success(
        `Đã ${newStatus === UserStatus.ACTIVE ? 'kích hoạt' : 'tạm ngưng'} tài khoản`,
      );
      fetchUserDetail(id!);
    } catch (error) {
      message.error('Có lỗi xảy ra khi cập nhật trạng thái');
    }
  };

  if (!user && !loading) {
    return (
      <div className="text-center py-8">
        <Title level={4}>Không tìm thấy người dùng</Title>
        <Button onClick={() => navigate('/user')}>
          <ArrowLeftOutlined /> Quay lại danh sách
        </Button>
      </div>
    );
  }

  const sessionColumns = [
    {
      title: 'Thiết bị',
      key: 'device',
      render: (_: any, record: UserSession) => (
        <div>
          <div className="font-medium">{record.device}</div>
          <div className="text-sm text-gray-500">{record.browser}</div>
        </div>
      ),
    },
    {
      title: 'Vị trí',
      key: 'location',
      render: (_: any, record: UserSession) => (
        <div>
          <div>{record.location}</div>
          <div className="text-sm text-gray-500">{record.ip_address}</div>
        </div>
      ),
    },
    {
      title: 'Hoạt động cuối',
      dataIndex: 'last_active',
      key: 'last_active',
      render: (date: string) => dayjs(date).format('HH:mm DD/MM/YYYY'),
    },
    {
      title: 'Trạng thái',
      key: 'status',
      render: (_: any, record: UserSession) => (
        <div>
          {record.is_current ? (
            <Tag color="green">Phiên hiện tại</Tag>
          ) : (
            <Tag color="default">Không hoạt động</Tag>
          )}
        </div>
      ),
    },
    {
      title: 'Hành động',
      key: 'actions',
      render: (_: any, record: UserSession) =>
        !record.is_current && (
          <Button
            size="small"
            danger
            onClick={() => handleRevokeSession(record.id)}
          >
            Thu hồi
          </Button>
        ),
    },
  ];

  return (
    <div className="user-detail">
      <ListHeader
        title={user?.display_name || 'Chi tiết người dùng'}
        showBackButton={true}
        backDestination="/user"
        customActions={
          <Space>
            <Button
              icon={<HistoryOutlined />}
              onClick={() => setShowActivityLogs(true)}
            >
              Nhật ký hoạt động
            </Button>
            <Button
              icon={<SafetyOutlined />}
              onClick={() => setShowRoleManager(true)}
            >
              Quản lý vai trò
            </Button>
            <Button
              icon={<EditOutlined />}
              type="primary"
              onClick={() => navigate(`/user/${id}/edit`)}
            >
              Chỉnh sửa
            </Button>
          </Space>
        }
      />

      {/* User Info Card */}
      <Card className="mb-6">
        <div className="flex items-center gap-4">
          <Badge
            status={
              user?.status === UserStatus.ACTIVE ? 'success' : 'error'
            }
            offset={[-8, 8]}
          >
            <Avatar
              size={80}
              src={user?.avatar_url}
              icon={<UserOutlined />}
            />
          </Badge>
          <div>
            <Title level={2} className="mb-1">
              {user?.display_name}
            </Title>
            <Text type="secondary" className="text-lg">
              {user?.email}
            </Text>
            <div className="mt-2">
              <Tag
                color={user?.status === UserStatus.ACTIVE ? 'green' : 'red'}
              >
                {user?.status === UserStatus.ACTIVE
                  ? 'Hoạt động'
                  : 'Tạm ngưng'}
              </Tag>
              <Tag color="blue">{user?.role}</Tag>
            </div>
          </div>
        </div>
      </Card>

      <Row gutter={24}>
        {/* Left Column */}
        <Col xs={24} lg={16}>
          <Tabs
            defaultActiveKey="info"
            items={[
              {
                key: 'info',
                label: 'Thông tin cơ bản',
                children: (
                  <Card>
                    <Descriptions column={2} bordered>
                      <Descriptions.Item label="ID" span={1}>
                        {user?.id}
                      </Descriptions.Item>
                      <Descriptions.Item label="Username" span={1}>
                        {user?.username}
                      </Descriptions.Item>
                      <Descriptions.Item label="Họ" span={1}>
                        {user?.first_name}
                      </Descriptions.Item>
                      <Descriptions.Item label="Tên" span={1}>
                        {user?.last_name}
                      </Descriptions.Item>
                      <Descriptions.Item label="Số điện thoại" span={1}>
                        <div className="flex items-center gap-2">
                          {user?.phone || 'Chưa có'}
                          {user?.phone_verified ? (
                            <CheckCircleOutlined className="text-green-500" />
                          ) : (
                            <CloseCircleOutlined className="text-red-500" />
                          )}
                        </div>
                      </Descriptions.Item>
                      <Descriptions.Item label="Email xác thực" span={1}>
                        <div className="flex items-center gap-2">
                          {user?.email_verified ? (
                            <>
                              <CheckCircleOutlined className="text-green-500" />
                              <span>Đã xác thực</span>
                            </>
                          ) : (
                            <>
                              <CloseCircleOutlined className="text-red-500" />
                              <span>Chưa xác thực</span>
                              <Button
                                size="small"
                                type="link"
                                onClick={handleResendVerification}
                              >
                                Gửi lại
                              </Button>
                            </>
                          )}
                        </div>
                      </Descriptions.Item>
                      <Descriptions.Item label="Ngôn ngữ" span={1}>
                        {user?.language}
                      </Descriptions.Item>
                      <Descriptions.Item label="Múi giờ" span={1}>
                        {user?.timezone}
                      </Descriptions.Item>
                      <Descriptions.Item label="2FA" span={1}>
                        <div className="flex items-center gap-2">
                          {user?.two_factor_enabled ? (
                            <Tag color="green">Đã bật</Tag>
                          ) : (
                            <Tag color="red">Chưa bật</Tag>
                          )}
                        </div>
                      </Descriptions.Item>
                      <Descriptions.Item label="Ngày tạo" span={1}>
                        {user?.created_at
                          ? dayjs(user.created_at).format('HH:mm DD/MM/YYYY')
                          : ''}
                      </Descriptions.Item>
                      <Descriptions.Item label="Cập nhật cuối" span={2}>
                        {user?.updated_at
                          ? dayjs(user.updated_at).format('HH:mm DD/MM/YYYY')
                          : ''}
                      </Descriptions.Item>
                    </Descriptions>
                  </Card>
                ),
              },
              {
                key: 'sessions',
                label: 'Phiên đăng nhập',
                children: (
                  <Card>
                    <div className="mb-4">
                      <Alert
                        message="Quản lý phiên đăng nhập"
                        description="Danh sách các thiết bị đã đăng nhập vào tài khoản này. Bạn có thể thu hồi các phiên không hoạt động."
                        type="info"
                        showIcon
                      />
                    </div>
                    <Table
                      columns={sessionColumns}
                      dataSource={userSessions}
                      pagination={false}
                      rowKey="id"
                      size="small"
                    />
                  </Card>
                ),
              },
            ]}
          />
        </Col>

        {/* Right Column */}
        <Col xs={24} lg={8}>
          {/* Statistics */}
          <Card title="Thống kê" className="mb-4">
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title="Tổng đăng nhập"
                  value={userStats?.total_logins}
                  prefix={<LoginOutlined />}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="Bài viết"
                  value={userStats?.posts_created}
                  prefix={<EditOutlined />}
                />
              </Col>
              <Col span={12} className="mt-4">
                <Statistic
                  title="Đã xuất bản"
                  value={userStats?.posts_published}
                  prefix={<CheckCircleOutlined />}
                />
              </Col>
              <Col span={12} className="mt-4">
                <Statistic
                  title="Bình luận"
                  value={userStats?.comments_made}
                  prefix={<MailOutlined />}
                />
              </Col>
            </Row>
            <Divider />
            <div className="text-sm text-gray-600">
              <div className="mb-2">
                <ClockCircleOutlined className="mr-2" />
                Đăng nhập cuối:{' '}
                {userStats?.last_login
                  ? dayjs(userStats.last_login).format('HH:mm DD/MM/YYYY')
                  : 'Chưa có'}
              </div>
              <div>
                <UserOutlined className="mr-2" />
                Tài khoản từ:{' '}
                {userStats?.account_created
                  ? dayjs(userStats.account_created).format('DD/MM/YYYY')
                  : ''}
              </div>
            </div>
          </Card>

          {/* Quick Actions */}
          <Card title="Hành động nhanh">
            <Space direction="vertical" className="w-full">
              <Button
                block
                icon={<LockOutlined />}
                onClick={handleResetPassword}
              >
                Reset mật khẩu
              </Button>
              <Button
                block
                icon={
                  user?.status === UserStatus.ACTIVE ? (
                    <CloseCircleOutlined />
                  ) : (
                    <CheckCircleOutlined />
                  )
                }
                onClick={handleToggleStatus}
                type={
                  user?.status === UserStatus.ACTIVE ? 'default' : 'primary'
                }
              >
                {user?.status === UserStatus.ACTIVE
                  ? 'Tạm ngưng tài khoản'
                  : 'Kích hoạt tài khoản'}
              </Button>
              {!user?.email_verified && (
                <Button
                  block
                  icon={<MailOutlined />}
                  onClick={handleResendVerification}
                >
                  Gửi lại email xác thực
                </Button>
              )}
            </Space>
          </Card>
        </Col>
      </Row>

      {/* Modals */}
      <UserActivityLogs
        visible={showActivityLogs}
        onClose={() => setShowActivityLogs(false)}
        userId={user?.id}
        userName={user?.display_name}
      />

      <UserRoleManager
        visible={showRoleManager}
        onClose={() => setShowRoleManager(false)}
        userId={user?.id}
        userName={user?.display_name}
        userEmail={user?.email}
      />
    </div>
  );
}

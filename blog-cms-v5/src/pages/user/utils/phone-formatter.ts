/**
 * Utility for E.164 phone number formatting
 * Used in user management form
 */

// Format phone number to E.164 format
export const formatPhoneToE164 = (phoneValue: any): string => {
  let formattedPhone = '';
  
  if (!phoneValue) {
    return formattedPhone;
  }
  
  if (typeof phoneValue === 'object' && phoneValue.phone) {
    // The antd-phone-input should return the complete international format
    const phoneNumber = phoneValue.phone;
    const countryCode = phoneValue.country?.dialCode;
    
    // Check if phone already starts with + (E.164 format)
    if (phoneNumber.startsWith('+')) {
      formattedPhone = phoneNumber;
    } else if (countryCode) {
      // Remove any leading zeros or non-digits from phone number
      const cleanPhone = phoneNumber.replace(/^\+?0*/, '').replace(/\D/g, '');
      formattedPhone = `+${countryCode}${cleanPhone}`;
    } else {
      formattedPhone = phoneNumber;
    }
  } else if (typeof phoneValue === 'string') {
    formattedPhone = phoneValue;
  }
  
  return formattedPhone;
};

// Parse E.164 phone number for PhoneInput component initialization
export const parsePhoneForInput = (e164Phone: string): any => {
  if (!e164Phone || !e164Phone.startsWith('+')) {
    return null;
  }
  
  // For E.164 format like "+16765126504", we need to parse it into the object format
  // that antd-phone-input expects: { countryCode, areaCode, phoneNumber, isoCode, valid }
  
  // Example: "+16765126504" -> US number with country code 1
  const phoneDigits = e164Phone.substring(1); // Remove the + sign
  
  // Simple parsing for common country codes
  let countryCode: number;
  let remainingDigits: string;
  let isoCode: string;
  
  if (phoneDigits.startsWith('1') && phoneDigits.length === 11) {
    // US/Canada (+1) - must be exactly 11 digits total (1 + 10 digit number)
    countryCode = 1;
    remainingDigits = phoneDigits.substring(1);
    isoCode = 'us';
  } else if (phoneDigits.startsWith('84')) {
    // Vietnam (+84)
    countryCode = 84;
    remainingDigits = phoneDigits.substring(2);
    isoCode = 'vn';
  } else if (phoneDigits.startsWith('44')) {
    // UK (+44)
    countryCode = 44;
    remainingDigits = phoneDigits.substring(2);
    isoCode = 'gb';
  } else {
    // Default fallback - try to parse as a generic number
    countryCode = parseInt(phoneDigits.substring(0, 2));
    remainingDigits = phoneDigits.substring(2);
    isoCode = 'us'; // Default
  }
  
  // For US numbers, split into area code and phone number
  let areaCode = '';
  let phoneNumber = remainingDigits;
  
  if (countryCode === 1 && remainingDigits.length === 10) {
    areaCode = remainingDigits.substring(0, 3);
    phoneNumber = remainingDigits.substring(3);
  }
  
  return {
    countryCode,
    areaCode,
    phoneNumber,
    isoCode,
    valid: () => true // Simple validation function
  };
};
import {
  CheckOutlined,
  CloseOutlined,
  EditOutlined,
  LockOutlined,
  SafetyOutlined,
  UserOutlined,
} from '@ant-design/icons';
import {
  But<PERSON>,
  Card,
  Checkbox,
  Col,
  Divider,
  Drawer,
  Form,
  message,
  Row,
  Select,
  Space,
  Table,
  Tag,
  Transfer,
  Typography,
  Alert,
} from 'antd';
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import StorageService, { StorageKeys } from '../../../services/storage.service';
import { MODULE } from '../config';

const { Title, Text } = Typography;

interface UserRoleManagerProps {
  visible: boolean;
  onClose: () => void;
  userId?: number;
  userName?: string;
  userEmail?: string;
}

interface Role {
  id: number;
  name: string;
  display_name: string;
  description: string;
  is_system: boolean;
  permissions: Permission[];
}

interface Permission {
  id: number;
  name: string;
  display_name: string;
  module: string;
  description: string;
}

interface UserRole {
  user_id: number;
  role_id: number;
  role: Role;
  granted_at: string;
  granted_by: number;
}

interface ApiUserRole {
  id: number;
  user_id: number;
  role_id: number;
  context_type: string;
  context_id: number;
  status: string;
  is_primary: boolean;
  is_temporary: boolean;
  valid_from: string;
  assigned_by: number;
  assigned_at: string;
  created_at: string;
  updated_at: string;
}

const UserRoleManager: React.FC<UserRoleManagerProps> = ({
  visible,
  onClose,
  userId,
  userName,
  userEmail,
}) => {
  const { t } = useTranslation(MODULE);
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [availableRoles, setAvailableRoles] = useState<Role[]>([]);
  const [userRoles, setUserRoles] = useState<UserRole[]>([]);
  const [selectedRoles, setSelectedRoles] = useState<number[]>([]);
  const [availablePermissions, setAvailablePermissions] = useState<
    Permission[]
  >([]);
  const [userPermissions, setUserPermissions] = useState<number[]>([]);
  const [activeTab, setActiveTab] = useState<'roles' | 'permissions'>('roles');

  useEffect(() => {
    if (visible && userId) {
      fetchData();
    }
  }, [visible, userId]);

  const fetchData = async () => {
    setLoading(true);
    try {
      // Get tenant_id from storage
      const tenantId = StorageService.get(StorageKeys.TENANT_ID);
      
      // Fetch roles and user roles from API
      const [rolesResponse, userRolesResponse] = await Promise.all([
        import('../../rbac-role/api').then(api => api.getAll()),
        import('../api').then(api => api.getUserRoles(userId!.toString()))
      ]);

      const fetchedRoles: Role[] = rolesResponse.data.map((role: any) => ({
        id: role.id,
        name: role.name,
        display_name: role.display_name || role.name,
        description: role.description || '',
        is_system: role.is_system || false,
        permissions: role.permissions || [],
      }));

      // Handle API response structure - data contains user_roles array
      const userRolesData = userRolesResponse.data.user_roles || userRolesResponse.data || [];
      
      // Ensure userRolesData is an array and not null
      const safeUserRolesData = Array.isArray(userRolesData) ? userRolesData : [];
      
      const fetchedUserRoles: UserRole[] = safeUserRolesData.map((apiUserRole: ApiUserRole) => ({
        user_id: apiUserRole.user_id,
        role_id: apiUserRole.role_id,
        role: fetchedRoles.find(r => r.id === apiUserRole.role_id),
        granted_at: apiUserRole.assigned_at,
        granted_by: apiUserRole.assigned_by,
      }));

      const mockPermissions: Permission[] = [
        {
          id: 1,
          name: 'blog.create',
          display_name: 'Tạo bài viết',
          group: 'Blog',
          description: 'Tạo bài viết mới',
        },
        {
          id: 2,
          name: 'blog.edit',
          display_name: 'Chỉnh sửa bài viết',
          group: 'Blog',
          description: 'Chỉnh sửa bài viết',
        },
        {
          id: 3,
          name: 'blog.delete',
          display_name: 'Xóa bài viết',
          group: 'Blog',
          description: 'Xóa bài viết',
        },
        {
          id: 4,
          name: 'blog.publish',
          display_name: 'Xuất bản bài viết',
          group: 'Blog',
          description: 'Xuất bản bài viết',
        },
        {
          id: 5,
          name: 'user.view',
          display_name: 'Xem người dùng',
          group: 'User',
          description: 'Xem danh sách người dùng',
        },
        {
          id: 6,
          name: 'user.edit',
          display_name: 'Chỉnh sửa người dùng',
          group: 'User',
          description: 'Chỉnh sửa thông tin người dùng',
        },
        {
          id: 7,
          name: 'user.delete',
          display_name: 'Xóa người dùng',
          group: 'User',
          description: 'Xóa người dùng',
        },
        {
          id: 8,
          name: 'category.manage',
          display_name: 'Quản lý danh mục',
          group: 'Category',
          description: 'Quản lý danh mục',
        },
      ];

      setAvailableRoles(fetchedRoles);
      setUserRoles(fetchedUserRoles);
      setSelectedRoles(fetchedUserRoles.map((ur) => ur.role_id));
      // Keep mock permissions for now - would need permissions API
      setAvailablePermissions(mockPermissions);
      setUserPermissions([1, 2, 4]); // Mock user permissions
    } catch (error) {
      console.error('Error fetching role data:', error);
      message.error('Không thể tải dữ liệu vai trò');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveRoles = async () => {
    setLoading(true);
    try {
      const currentRoleIds = userRoles.map(ur => ur.role_id);
      const rolesToAdd = selectedRoles.filter(roleId => !currentRoleIds.includes(roleId));
      const rolesToRemove = currentRoleIds.filter(roleId => !selectedRoles.includes(roleId));

      // Import API functions
      const userApi = await import('../api');

      // Add new roles
      for (const roleId of rolesToAdd) {
        await userApi.assignRoleToUser(userId!, roleId);
      }

      // Remove roles
      for (const roleId of rolesToRemove) {
        await userApi.revokeRoleFromUser(userId!, roleId);
      }

      message.success('Đã cập nhật vai trò thành công');
      await fetchData(); // Refresh data
    } catch (error) {
      console.error('Error updating roles:', error);
      message.error('Có lỗi xảy ra khi cập nhật vai trò');
    } finally {
      setLoading(false);
    }
  };

  const handleSavePermissions = async () => {
    setLoading(true);
    try {
      // TODO: Implement API call to update user permissions
      console.log('Updating user permissions:', userPermissions);

      message.success('Đã cập nhật quyền thành công');
    } catch (error) {
      message.error('Có lỗi xảy ra khi cập nhật quyền');
    } finally {
      setLoading(false);
    }
  };

  const roleColumns = [
    {
      title: 'Vai trò',
      dataIndex: 'display_name',
      key: 'display_name',
      render: (text: string, record: Role) => (
        <div>
          <div className="font-medium">{text}</div>
          <div className="text-sm text-gray-500">{record.name}</div>
        </div>
      ),
    },
    {
      title: 'Mô tả',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: 'Loại',
      dataIndex: 'is_system',
      key: 'is_system',
      render: (isSystem: boolean) => (
        <Tag color={isSystem ? 'red' : 'blue'}>
          {isSystem ? 'Hệ thống' : 'Tùy chỉnh'}
        </Tag>
      ),
    },
    {
      title: 'Trạng thái',
      key: 'status',
      render: (_: any, record: Role) => {
        const isAssigned = selectedRoles.includes(record.id);
        return (
          <Tag color={isAssigned ? 'green' : 'default'}>
            {isAssigned ? (
              <>
                <CheckOutlined /> Đã gán
              </>
            ) : (
              <>
                <CloseOutlined /> Chưa gán
              </>
            )}
          </Tag>
        );
      },
    },
  ];

  // Group permissions by module
  const groupedPermissions = availablePermissions.reduce(
    (acc, permission) => {
      if (!acc[permission.module]) {
        acc[permission.module] = [];
      }
      acc[permission.module].push(permission);
      return acc;
    },
    {} as Record<string, Permission[]>,
  );

  return (
    <Drawer
      title={
        <div className="flex items-center gap-2">
          <SafetyOutlined />
          <span>Quản lý vai trò - {userName}</span>
        </div>
      }
      placement="right"
      size="large"
      open={visible}
      onClose={onClose}
      extra={
        <Space>
          <Button
            onClick={() => setActiveTab('roles')}
            type={activeTab === 'roles' ? 'primary' : 'default'}
          >
            Vai trò
          </Button>
          <Button
            onClick={() => setActiveTab('permissions')}
            type={activeTab === 'permissions' ? 'primary' : 'default'}
          >
            Quyền hạn
          </Button>
        </Space>
      }
    >
      <div className="mb-4">
        <Text strong>Email: </Text>
        <Text>{userEmail}</Text>
      </div>

      {activeTab === 'roles' && (
        <div>
          <Alert
            message="Quản lý vai trò"
            description="Vai trò xác định các quyền hạn cơ bản của người dùng. Một người dùng có thể có nhiều vai trò."
            type="info"
            showIcon
            className="mb-4"
          />

          <Card title="Chọn vai trò" className="mb-4">
            <Checkbox.Group
              value={selectedRoles}
              onChange={setSelectedRoles}
              className="w-full"
            >
              <Row gutter={[16, 16]}>
                {availableRoles.map((role) => (
                  <Col span={12} key={role.id}>
                    <Card size="small" className="h-full">
                      <div className="flex items-start gap-3">
                        <Checkbox value={role.id} />
                        <div className="flex-1">
                          <div className="font-medium">{role.display_name}</div>
                          <div className="text-sm text-gray-600 mb-2">
                            {role.description}
                          </div>
                          <Tag
                            color={role.is_system ? 'red' : 'blue'}
                            size="small"
                          >
                            {role.is_system ? 'Hệ thống' : 'Tùy chỉnh'}
                          </Tag>
                        </div>
                      </div>
                    </Card>
                  </Col>
                ))}
              </Row>
            </Checkbox.Group>
          </Card>

          <Card title="Vai trò hiện tại">
            <Table
              columns={roleColumns}
              dataSource={availableRoles}
              pagination={false}
              rowKey="id"
              size="small"
            />
          </Card>

          <div className="mt-4 text-right">
            <Button
              type="primary"
              onClick={handleSaveRoles}
              loading={loading}
              icon={<CheckOutlined />}
            >
              Lưu thay đổi
            </Button>
          </div>
        </div>
      )}

      {activeTab === 'permissions' && (
        <div>
          <Alert
            message="Quản lý quyền hạn"
            description="Quyền hạn chi tiết cho phép kiểm soát cụ thể các hành động mà người dùng có thể thực hiện."
            type="info"
            showIcon
            className="mb-4"
          />

          {Object.entries(groupedPermissions).map(([group, permissions]) => (
            <Card key={group} title={group} className="mb-4" size="small">
              <Checkbox.Group
                value={userPermissions}
                onChange={setUserPermissions}
                className="w-full"
              >
                <Row gutter={[16, 8]}>
                  {permissions.map((permission) => (
                    <Col span={12} key={permission.id}>
                      <div className="flex items-start gap-2 p-2 border rounded">
                        <Checkbox value={permission.id} />
                        <div className="flex-1">
                          <div className="font-medium text-sm">
                            {permission.display_name}
                          </div>
                          <div className="text-xs text-gray-500">
                            {permission.description}
                          </div>
                          <Text code className="text-xs">
                            {permission.name}
                          </Text>
                        </div>
                      </div>
                    </Col>
                  ))}
                </Row>
              </Checkbox.Group>
            </Card>
          ))}

          <div className="mt-4 text-right">
            <Button
              type="primary"
              onClick={handleSavePermissions}
              loading={loading}
              icon={<CheckOutlined />}
            >
              Lưu quyền hạn
            </Button>
          </div>
        </div>
      )}
    </Drawer>
  );
};

export default UserRoleManager;

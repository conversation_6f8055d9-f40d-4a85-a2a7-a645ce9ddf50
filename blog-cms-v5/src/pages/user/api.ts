import { ApiResponse, apiService } from '../../services/api.service';
import ConsoleService from '../../services/console.service';
import { SelectOption } from '../../types.global';
import {
  UserResponse,
  UserCreateRequest,
  UserUpdateRequest,
  UserListFilter,
  UserListResponse,
  UserSearchRequest,
  UserSearchResponse,
  UserStatsResponse,
  TwoFactorSetupResponse,
} from './type';
const logger = ConsoleService.register('user');
const url = `/api/cms/v1/users`;
const url2fa = `/api/cms/v1/2fa`;

export async function getUsers(
  filter?: UserListFilter,
): Promise<UserListResponse> {
  // Flatten pagination parameters for cursor-based pagination
  const params: any = { ...filter };
  if (params.pagination) {
    // Extract pagination parameters to top level
    if (params.pagination.cursor) {
      params.cursor = params.pagination.cursor;
    }
    if (params.pagination.limit) {
      params.limit = params.pagination.limit;
    }
    // Remove nested pagination object
    delete params.pagination;
  }
  
  const res = await apiService.get(url, { params });
  return res.data;
}

export async function getUser(id: string): Promise<ApiResponse<UserResponse>> {
  const res = await apiService.get(`${url}/${id}`);
  return res.data;
}

export async function createUser(
  payload: UserCreateRequest,
): Promise<ApiResponse<UserResponse>> {
  const res = await apiService.post(url, payload);
  return res.data;
}

export async function updateUser(
  id: string,
  payload: UserUpdateRequest,
): Promise<ApiResponse<UserResponse>> {
  const res = await apiService.put(`${url}/${id}`, payload);
  return res.data;
}

export async function deleteUser(
  id: string,
): Promise<ApiResponse<UserResponse>> {
  return apiService.delete(`${url}/${id}`);
}

export async function getOptions(): Promise<ApiResponse<SelectOption[]>> {
  const res = await apiService.get(`${url}/options`);
  return res.data;
}

export async function getUserOptions(
  search?: string,
): Promise<UserResponse[]> {
  const params: UserListFilter = { 
    pagination: { limit: 20 }
  };
  if (search) {
    params.search_query = search;
  }
  const response = await getUsers(params);
  return response.users || [];
}

export async function searchUsers(
  request: UserSearchRequest,
): Promise<UserSearchResponse> {
  const res = await apiService.post(`${url}/search`, request);
  return res.data;
}

export async function getAllUsers(): Promise<ApiResponse<UserResponse[]>> {
  const res = await apiService.get(`${url}/all`);
  return res.data;
}

export async function getUserProfile(): Promise<ApiResponse<UserResponse>> {
  const res = await apiService.get(`/api/cms/v1/auth/profile`);
  return res.data;
}

export async function updateUserProfile(
  payload: UserUpdateRequest,
): Promise<ApiResponse<UserResponse>> {
  const res = await apiService.put<ApiResponse<UserResponse>>(
    `/api/cms/v1/auth/profile`,
    payload,
  );
  return res.data;
}

export async function changePassword(payload: {
  current_password: string;
  new_password: string;
  confirm_password: string;
}): Promise<ApiResponse<{ sessions_invalidated: boolean }>> {
  const res = await apiService.post<
    ApiResponse<{ sessions_invalidated: boolean }>
  >(`/api/cms/v1/auth/change-password`, payload);
  return res.data;
}

export async function getPermissions(): Promise<ApiResponse<any>> {
  const res = await apiService.get(`${url}/permissions`);
  return res.data;
}

// User Roles API
export async function getUserRoles(
  userId: string,
  options?: { context_type?: string; context_id?: number }
): Promise<ApiResponse<any>> {
  const params = new URLSearchParams();
  if (options?.context_type) {
    params.append('context_type', options.context_type);
  }
  if (options?.context_id) {
    params.append('context_id', options.context_id.toString());
  }
  
  const queryString = params.toString();
  const url = `/api/cms/v1/rbac/users/${userId}/roles${queryString ? `?${queryString}` : ''}`;
  
  const res = await apiService.get(url);
  return res.data;
}

export async function assignRoleToUser(
  userId: number,
  roleId: number,
): Promise<ApiResponse<any>> {
  const res = await apiService.post(`/api/cms/v1/rbac/users/${userId}/roles`, {
    user_id: userId,
    role_id: roleId,
    // Context is handled by backend middleware - no need to send from client
  });
  return res.data;
}

export async function assignMultipleRolesToUser(
  userId: number,
  roleIds: number[],
): Promise<ApiResponse<any>> {
  // Use Promise.all to make multiple API calls in parallel
  const promises = roleIds.map((roleId) =>
    apiService.post(`/api/cms/v1/rbac/users/${userId}/roles`, {
      user_id: userId,
      role_id: roleId,
      // Context is handled by backend middleware - no need to send from client
    }),
  );

  await Promise.all(promises);

  // Return a successful response
  return {
    data: { success: true },
    status: {
      success: true,
      code: 200,
      message: 'Roles assigned successfully',
    },
  };
}

export async function revokeRoleFromUser(
  userId: number,
  roleId: number,
): Promise<ApiResponse<any>> {
  const res = await apiService.delete(
    `/api/cms/v1/rbac/users/${userId}/roles/${roleId}`,
  );
  return res.data;
}

// User Statistics API
export async function getUserStats(): Promise<ApiResponse<UserStatsResponse>> {
  const res = await apiService.get(`${url}/stats`);
  return res.data;
}

// Two-Factor Authentication API
export async function setupTwoFactor(): Promise<
  ApiResponse<TwoFactorSetupResponse>
> {
  const res = await apiService.post(`${url2fa}/setup`);
  return res.data;
}

export async function enableTwoFactor(
  token: string,
): Promise<ApiResponse<any>> {
  try {
    const res = await apiService.post(`${url2fa}/enable`, { token });
    return res.data;
  } catch (error: any) {
    logger('[enableTwoFactor] error:', error);
    if (error.response?.data?.status?.code === 400) {
      return error.response.data;
    }
    return error;
  }
}

export async function disableTwoFactor(
  token: string,
): Promise<ApiResponse<any>> {
  const res = await apiService.post(`${url2fa}/disable`, { token });
  return res.data;
}

export async function verifyTwoFactor(
  token: string,
): Promise<ApiResponse<any>> {
  const res = await apiService.post(`${url2fa}/verify`, { token });
  return res.data;
}

// Legacy compatibility functions (deprecated)
export const getItems = getUsers;
export const getItem = getUser;
export const createItem = createUser;
export const updateItem = updateUser;
export const deleteItem = deleteUser;
export const getSearch = searchUsers;
export const getAll = getAllUsers;
export const getProfile = getUserProfile;
export const updateProfile = updateUserProfile;
export const updatePassword = changePassword;
export const generateTotp = setupTwoFactor;
export const getTotpStatus = () =>
  apiService.get(`${url2fa}/status`).then((res) => res.data);
export const verifyTotp = verifyTwoFactor;
export const enableTotp = enableTwoFactor;
export const disableTotp = disableTwoFactor;

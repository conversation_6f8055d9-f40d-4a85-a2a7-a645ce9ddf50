import { SaveOutlined, UserSwitchOutlined } from '@ant-design/icons';
import {
  Button,
  Col,
  Divider,
  Form,
  Input,
  message,
  Row,
  Select,
  Spin,
} from 'antd';
import _ from 'lodash';
import React, { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import PhoneInput from 'antd-phone-input';

import { SaveBarTop } from '../../../components/save-bar';
import ConsoleService from '../../../services/console.service';
import { extractErrorMessage } from '../../../services/api.service';
import tenantService from '../../../services/tenant.service';
import { getItems as getRoles } from '../../rbac-role/api';
import { RbacRole } from '../../rbac-role/type';
import {
  assignRoleToUser,
  createItem,
  getItem,
  getUserRoles,
  updateItem,
} from '../api';
import { MODULE } from '../config';
import { User } from '../type';
import { formatPhoneToE164, parsePhoneForInput } from '../utils/phone-formatter';

const FormItem = Form.Item;

interface IndexFormProps {
  onChange: (reload: boolean) => void;
  id?: string;
}

const IndexForm: React.FC<IndexFormProps> = ({ onChange, id }) => {
  const { t } = useTranslation(MODULE);
  const logger = ConsoleService.register(MODULE);
  const [form] = Form.useForm();
  const [isNew, setIsNew] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [rolesLoading, setRolesLoading] = useState<boolean>(false);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [roles, setRoles] = useState<RbacRole[]>([]);
  const [phoneValue, setPhoneValue] = useState<any>(null);
  const initForm = {};

  const formatData = useCallback(
    (item: User) => {
      logger(item);
      // If the user has a role_id, make sure it's properly formatted as a number
      if (item.role_id) {
        item.role_id = Number(item.role_id);
      }
      return item;
    },
    [logger],
  );

  const getItemData = useCallback(
    async (_id: string) => {
      setLoading(true);
      try {
        const res = await getItem(_id);
        if (res.status.success) {
          // Get user roles if this is an existing user
          try {
            const userRolesRes = await getUserRoles(_id);
            if (userRolesRes.status.success && userRolesRes.data.user_roles && userRolesRes.data.user_roles.length > 0) {
              // Extract all role IDs from the user roles response
              const roleIds = userRolesRes.data.user_roles.map(
                (role: { role_id: number }) => role.role_id,
              );

              // Set the form values with all the user's roles
              const userData = formatData({
                ...res.data,
                role_ids: roleIds,
              });
              form.setFieldsValue(userData);
              // Set phone value state for PhoneInput component
              console.log('User phone data:', userData.phone);
              if (userData.phone) {
                // Parse E.164 phone number for PhoneInput component
                const parsedPhone = parsePhoneForInput(userData.phone);
                console.log('Parsed phone for input:', parsedPhone);
                setPhoneValue(parsedPhone);
              } else {
                console.log('No phone data found, setting null');
                setPhoneValue(null);
              }
            } else {
              const userData = formatData(res.data);
              form.setFieldsValue(userData);
              // Set phone value state for PhoneInput component
              console.log('User phone data:', userData.phone);
              if (userData.phone) {
                // Parse E.164 phone number for PhoneInput component
                const parsedPhone = parsePhoneForInput(userData.phone);
                console.log('Parsed phone for input:', parsedPhone);
                setPhoneValue(parsedPhone);
              } else {
                console.log('No phone data found, setting null');
                setPhoneValue(null);
              }
            }
          } catch (roleError) {
            logger('Error fetching user roles', roleError);
            const userData = formatData(res.data);
            form.setFieldsValue(userData);
            // Set phone value state for PhoneInput component
            if (userData.phone) {
              setPhoneValue(userData.phone);
            }
          }
        } else {
          message.error(res.status.message);
        }
      } catch (error) {
        logger(error);
      } finally {
        setLoading(false);
      }
    },
    [form, logger, formatData],
  );

  const fetchRoles = useCallback(async () => {
    setRolesLoading(true);
    try {
      const res = await getRoles({ limit: 100 });
      if (res.status.success) {
        setRoles(res.data);
      }
    } catch (error) {
      logger('Error fetching roles', error);
      message.error(t('Không thể tải danh sách vai trò'));
    } finally {
      setRolesLoading(false);
    }
  }, [logger, t]);

  useEffect(() => {
    form.resetFields();
    setPhoneValue(null); // Reset phone value state
    if (id) {
      getItemData(id);
      setIsNew(false);
    } else {
      setIsNew(true);
    }

    // Fetch roles when component mounts
    fetchRoles();
  }, [form, getItemData, id, fetchRoles]);

  // Helper function to assign multiple roles to user
  const assignRolesToUserHelper = async (
    userId: number,
    roleIds: number[],
    isNewUser: boolean,
  ) => {
    try {
      // Use Promise.all to make multiple API calls in parallel
      const promises = roleIds.map((roleId) =>
        assignRoleToUser(userId, roleId),
      );

      await Promise.all(promises);
      return true;
    } catch (roleError) {
      logger(
        `Error assigning roles to ${isNewUser ? 'new' : ''} user`,
        roleError,
      );
      message.warning(
        isNewUser
          ? t('Tài khoản đã được tạo nhưng không thể gán vai trò')
          : t('Tài khoản đã được cập nhật nhưng không thể gán vai trò'),
      );
      return false;
    }
  };

  // Helper function to handle successful user creation/update
  const handleSuccess = (res: any, values: User, isNewUser: boolean) => {
    // Show success message
    message.success(isNewUser ? t('addSuccess') : t('updateSuccess'));

    // If roles were selected, assign them to the user
    if (values.role_ids && values.role_ids.length > 0) {
      assignRolesToUserHelper(res.data.id, values.role_ids, isNewUser);
    } else if (values.role_id) {
      // For backward compatibility
      assignRolesToUserHelper(res.data.id, [values.role_id], isNewUser);
    }

    // Update form and notify parent
    form.resetFields();
    onChange(true);
  };

  const onFinish = async (values: User) => {
    setSubmitting(true);
    try {
      // Prepare data according to API DTO  
      // Note: API still requires tenant_id in payload, even though headers are also sent
      const tenantId = parseInt(tenantService.getSelectedTenantId() || '1');
      
      // Format phone number to E.164 format if provided
      const formattedPhone = formatPhoneToE164(values.phone);
      
      const submitData = {
        ...values,
        tenant_id: tenantId, // Required by API DTO validation
        display_name: `${values.first_name} ${values.last_name}`, // Create display name from first + last name
        send_welcome_email: false, // Set default value
        role: 'user', // Set default role, we'll handle role_ids separately
        phone: formattedPhone, // Use formatted phone number
      };

      // Remove old fields if they exist
      delete submitData.full_name;
      delete submitData.role_ids; // Remove role_ids from API call, handle separately

      console.log('Submitting user data:', submitData);

      let res;
      if (isNew) {
        res = await createItem(submitData);
      } else {
        res = await updateItem(id!, submitData);
      }

      if (res.status.success) {
        handleSuccess(res, values, isNew);
      } else {
        message.error(res.status.message);
      }
    } catch (error) {
      logger('Error submitting form', error);
      const errorMsg = extractErrorMessage(error) || 
                      (isNew ? t('Tạo tài khoản thất bại') : t('Cập nhật tài khoản thất bại'));
      message.error(errorMsg);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Spin spinning={loading} tip={t('Đang tải dữ liệu...')}>
      <Form
        form={form}
        name="form"
        layout="vertical"
        onFinish={onFinish}
        autoComplete="off"
        initialValues={initForm}
        disabled={loading}
      >
        <SaveBarTop
          group={t('TÀI KHOẢN')}
          action={t('THÊM TÀI KHOẢN')}
          icon={<UserSwitchOutlined style={{ fontSize: '28px' }} />}
        >
          <Button
            type="primary"
            htmlType="submit"
            loading={submitting}
            icon={<SaveOutlined />}
            size="large"
            disabled={loading}
          >
            {isNew ? t('btnSave') : t('btnUpdate')}
          </Button>
        </SaveBarTop>

        <div className="p-4">
          <Row gutter={16}>
            <Col xs={24} lg={24}>
              <Row gutter={16}>
                <Col xs={24} lg={6}>
                  <h2>{t('Thông tin chung')}</h2>
                </Col>
                <Col xs={24} lg={18}>
                  <Row gutter={16}>
                    <Col xs={24} lg={12}>
                      <FormItem
                        label={t('Họ')}
                        name="first_name"
                        rules={[
                          { required: true, message: t('pleaseEnterData') },
                        ]}
                      >
                        <Input placeholder="Nhập họ" />
                      </FormItem>
                    </Col>
                    <Col xs={24} lg={12}>
                      <FormItem
                        label={t('Tên')}
                        name="last_name"
                        rules={[
                          { required: true, message: t('pleaseEnterData') },
                        ]}
                      >
                        <Input placeholder="Nhập tên" />
                      </FormItem>
                    </Col>
                    <Col xs={24} lg={12}>
                      <FormItem
                        label={t('Email')}
                        name="email"
                        rules={[
                          { required: true, message: t('pleaseEnterData') },
                          { type: 'email', message: t('Email không hợp lệ') },
                        ]}
                      >
                        <Input />
                      </FormItem>
                    </Col>
                    <Col xs={24} lg={12}>
                      <FormItem
                        label={t('Số điện thoại')}
                        name="phone"
                        rules={[
                          {
                            validator: (_, value) => {
                              if (!value) {
                                return Promise.resolve();
                              }
                              
                              // Handle various phone input formats
                              let phoneToValidate = '';
                              if (typeof value === 'string') {
                                phoneToValidate = value;
                              } else if (typeof value === 'object' && value.phone) {
                                // Handle antd-phone-input object format
                                phoneToValidate = value.phone;
                              }
                              
                              // Basic phone number validation - at least 6 digits
                              if (phoneToValidate && phoneToValidate.replace(/\D/g, '').length < 6) {
                                return Promise.reject(new Error('Vui lòng nhập số điện thoại hợp lệ (tối thiểu 6 chữ số)'));
                              }
                              
                              return Promise.resolve();
                            },
                          },
                        ]}
                      >
                        <PhoneInput
                          placeholder={t('Nhập số điện thoại')}
                          searchPlaceholder="Tìm quốc gia"
                          enableSearch
                          country="vn"
                          disableDropdown={false}
                          value={phoneValue}
                          onChange={(value) => {
                            // Store the complete value for processing during submit
                            console.log('PhoneInput onChange:', value);
                            setPhoneValue(value);
                            form.setFieldValue('phone', value);
                          }}
                        />
                      </FormItem>
                    </Col>
                    <Col xs={24} lg={12}>
                      <FormItem
                        label="Vai trò"
                        name="role_ids"
                        rules={[
                          {
                            required: true,
                            message: t('Vui lòng chọn ít nhất một vai trò'),
                          },
                        ]}
                      >
                        <Select
                          mode="multiple"
                          placeholder={t('Chọn vai trò')}
                          loading={rolesLoading}
                          notFoundContent={
                            rolesLoading ? (
                              <Spin size="small" />
                            ) : (
                              t('Không có vai trò nào')
                            )
                          }
                          optionFilterProp="children"
                          showSearch
                          allowClear
                        >
                          {roles.map((role) => (
                            <Select.Option
                              key={role.id}
                              value={role.id}
                            >
                              {role.display_name || role.name}
                            </Select.Option>
                          ))}
                        </Select>
                      </FormItem>
                    </Col>
                    {!isNew && (
                      <Col xs={24} lg={12}>
                        <FormItem
                          label={t('Trạng thái')}
                          name="status"
                          rules={[
                            { required: false, message: t('pleaseEnterData') },
                          ]}
                        >
                          <Select>
                            <Select.Option value="active">Active</Select.Option>
                            <Select.Option value="inactive">
                              Inactive
                            </Select.Option>
                            <Select.Option value="suspended">
                              Suspended
                            </Select.Option>
                            <Select.Option value="banned">Banned</Select.Option>
                            <Select.Option value="locked">Locked</Select.Option>
                          </Select>
                        </FormItem>
                      </Col>
                    )}
                  </Row>
                </Col>
              </Row>

              <Divider />
              <Row gutter={16}>
                <Col xs={24} lg={6}>
                  <h2>{t('Tài khoản')}</h2>
                </Col>
                <Col xs={24} lg={18}>
                  <Row gutter={16}>
                    <Col xs={24} lg={12}>
                      <FormItem
                        label={t('Tên đăng nhập')}
                        name="username"
                        rules={[
                          { required: true, message: t('pleaseEnterData') },
                          { min: 3, message: t('Tối thiểu 3 ký tự') },
                          { max: 50, message: t('Tối đa 50 ký tự') },
                        ]}
                      >
                        <Input />
                      </FormItem>
                    </Col>
                    {isNew && (
                      <Col xs={24} lg={12}>
                        <FormItem
                          label={t('Mật khẩu')}
                          name="password"
                          rules={[
                            { required: true, message: t('pleaseEnterData') },
                            {
                              min: 8,
                              message: t('Mật khẩu phải ít nhất 8 ký tự'),
                            },
                          ]}
                        >
                          <Input.Password />
                        </FormItem>
                      </Col>
                    )}
                  </Row>
                </Col>
              </Row>
            </Col>
          </Row>
        </div>
      </Form>
    </Spin>
  );
};

export default IndexForm;

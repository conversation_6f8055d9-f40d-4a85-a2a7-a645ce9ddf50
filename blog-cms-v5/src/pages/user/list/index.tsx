import {
  CheckCircleOutlined,
  CheckOutlined,
  CloseCircleOutlined,
  DeleteOutlined,
  DownloadOutlined,
  DownOutlined,
  EditOutlined,
  EyeOutlined,
  HistoryOutlined,
  KeyOutlined,
  MailOutlined,
  PlusCircleOutlined,
  SafetyOutlined,
  StopOutlined,
  UploadOutlined,
  UserOutlined,
} from '@ant-design/icons';
import {
  Avatar,
  Button,
  Col,
  Dropdown,
  message,
  Pagination,
  Popconfirm,
  Row,
  Space,
  Table,
  Tag,
  Tooltip,
} from 'antd';
import dayjs from 'dayjs';
import queryString from 'query-string';
import { useState, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import { useCursorPagination } from '../../../components/pagination';
import CursorPagination from '../../../components/pagination/cursor-pagination';
import { ListHeader } from '../../../components/list-header/list-header';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import {
  //changeBrowserLocation,
  getPageNumber,
} from '../../../services/utils.service';
import { deleteUser, getUsers, updateUser } from '../api';
import '../assets/styles.scss';
import { MODULE, MODULE_POPUP } from '../config';
import ModalForm from '../form/modal';
import useUserStore from '../store';
import { UserResponse, UserListFilter, UserStatus } from '../type';
import Search from './search';
import InviteUserModal from '../components/InviteUserModal';
import ImportUserModal from '../components/ImportUserModal';
import UserActivityLogs from '../components/UserActivityLogs';
import UserRoleManager from '../components/UserRoleManager';
import { getAll as getRoles } from '../../rbac-role/api';
import { RbacRole } from '../../rbac-role/type';

export default function List() {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation(MODULE);
  const navigate = useNavigateTenant();
  const { pathname, search } = useLocation();
  const query = queryString.parse(search);
  
  // Cursor pagination hook
  const {
    afterKey,
    isNext,
    isBack,
    setNextCursor,
    goNext,
    goBack,
    resetPagination,
    limit,
  } = useCursorPagination({
    defaultLimit: 10,
  });
  
  const [loading, setLoading] = useState<boolean>(false);
  const [filters, setFilters] = useState({});
  const [showModal, setShowModal] = useState(false);
  const [items, setItems] = useState<UserResponse[]>([]);
  const [idCurrent, setIdCurrent] = useState<string | undefined>();
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [showImportModal, setShowImportModal] = useState(false);
  const [showActivityLogs, setShowActivityLogs] = useState(false);
  const [showRoleManager, setShowRoleManager] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserResponse | null>(null);
  const [roleMap, setRoleMap] = useState<Map<number, RbacRole>>(new Map());

  // Fetch roles for role name lookup
  const fetchRoles = async () => {
    try {
      const rolesResponse = await getRoles();
      if (rolesResponse.status.success && rolesResponse.data) {
        const rolesMap = new Map<number, RbacRole>();
        rolesResponse.data.forEach((role: RbacRole) => {
          rolesMap.set(role.id, role);
        });
        setRoleMap(rolesMap);
      }
    } catch (error) {
      logger.error('Failed to fetch roles:', error);
    }
  };

  const fetchData = useCallback(async (payload?: any, cursor?: string) => {
    setLoading(true);
    const params: UserListFilter = {
      ...filters,
      ...payload,
      pagination: {
        cursor: cursor || afterKey,
        limit: limit,
      },
    };

    try {
      const response = await getUsers(params);
      // Access data from the standard API response format
      const data = Array.isArray(response.data) ? response.data : [];
      setItems(data);
      
      // Handle cursor pagination
      setNextCursor(response.meta?.next_cursor);
      
      logger('[fetchData] Success:', { 
        dataLength: data.length, 
        nextCursor: response.meta?.next_cursor 
      });
    } catch (error) {
      logger.error('Failed to fetch users:', error);
      setItems([]);
      setNextCursor(undefined);
    } finally {
      setLoading(false);
    }
  }, [filters, afterKey, limit, setNextCursor, logger]);

  const handleFilters = useCallback((values: any) => {
    logger('[filters]', { filters, values });
    // Reset pagination when filters change
    resetPagination();
    setFilters(values);
    fetchData(values);
  }, [logger, filters, resetPagination, fetchData]);

  const [isInitialLoad, setIsInitialLoad] = useState(true);

  // Initial load effect
  useEffect(() => {
    if (isInitialLoad) {
      fetchData(undefined, afterKey);
      fetchRoles();
      setIsInitialLoad(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Handle pagination changes
  useEffect(() => {
    if (!isInitialLoad) {
      fetchData(undefined, afterKey);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [afterKey]);

  const handleDelete = async (id: string) => {
    try {
      const res = await deleteUser(id);
      if (res.status.success) {
        message.success(t('deleteSuccess'));
        fetchData(undefined, afterKey);
      } else {
        message.error(res.status.message);
      }
    } catch (error: any) {
      message.error(error?.message || 'Failed to delete user');
    }
  };

  const handleBulkDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('Vui lòng chọn ít nhất một người dùng để xóa');
      return;
    }

    try {
      const promises = selectedRowKeys.map((id) => deleteUser(id.toString()));
      await Promise.all(promises);
      message.success(`Đã xóa ${selectedRowKeys.length} người dùng thành công`);
      setSelectedRowKeys([]);
      fetchData(undefined, afterKey);
    } catch (error: any) {
      message.error('Có lỗi xảy ra khi xóa người dùng');
    }
  };

  const handleBulkStatusUpdate = async (status: UserStatus) => {
    if (selectedRowKeys.length === 0) {
      message.warning('Vui lòng chọn ít nhất một người dùng để cập nhật');
      return;
    }

    try {
      // This would need to be implemented in the API
      message.success(
        `Đã cập nhật trạng thái cho ${selectedRowKeys.length} người dùng`,
      );
      setSelectedRowKeys([]);
      fetchData(undefined, afterKey);
    } catch (error: any) {
      message.error('Có lỗi xảy ra khi cập nhật trạng thái');
    }
  };

  const handleExport = async () => {
    try {
      // This would need to be implemented in the API
      message.success('Đang xuất dữ liệu người dùng...');
    } catch (error: any) {
      message.error('Có lỗi xảy ra khi xuất dữ liệu');
    }
  };

  const handleActions = (action: string, record: any) => {
    if (action === 'edit') {
      if (MODULE_POPUP) {
        setIdCurrent(record.id.toString());
        setShowModal(true);
      } else {
        navigate(`/${MODULE}/${record.id}`);
      }
    } else if (action === 'add') {
      if (MODULE_POPUP) {
        setIdCurrent(undefined);
        setShowModal(true);
      } else {
        navigate(`/${MODULE}/create`);
      }
    } else if (action === 'detail') {
      navigate(`/${MODULE}/${record.id}`);
    } else if (action === 'reset_password') {
      // Implement reset password functionality
      message.info('Chức năng reset mật khẩu sẽ được triển khai');
    } else if (action === 'toggle_status') {
      // Toggle user status
      const newStatus =
        record.status === UserStatus.ACTIVE
          ? UserStatus.INACTIVE
          : UserStatus.ACTIVE;
      handleStatusUpdate(record.id, newStatus);
    } else if (action === 'view_logs') {
      setSelectedUser(record);
      setShowActivityLogs(true);
    } else if (action === 'manage_roles') {
      setSelectedUser(record);
      setShowRoleManager(true);
    }
  };

  const handleStatusUpdate = async (userId: number, status: UserStatus) => {
    try {
      await updateUser(userId.toString(), { status });
      message.success('Đã cập nhật trạng thái người dùng');
      fetchData(undefined, afterKey);
    } catch (error: any) {
      message.error('Có lỗi xảy ra khi cập nhật trạng thái');
    }
  };

  const handleRefresh = () => {
    resetPagination();
    fetchData();
  };

  const handleModal = () => {
    setShowModal(false);
    fetchData(undefined, afterKey);
  };

  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
    onSelectAll: (
      selected: boolean,
      selectedRows: UserResponse[],
      changeRows: UserResponse[],
    ) => {
      logger('Select all:', { selected, selectedRows, changeRows });
    },
  };

  const bulkActionMenuItems = [
    {
      key: 'bulk_delete',
      label: (
        <Popconfirm
          title="Xác nhận xóa"
          description={`Bạn có chắc chắn muốn xóa ${selectedRowKeys.length} người dùng đã chọn?`}
          onConfirm={handleBulkDelete}
          okText="Xóa"
          cancelText="Hủy"
        >
          <DeleteOutlined /> Xóa đã chọn ({selectedRowKeys.length})
        </Popconfirm>
      ),
      disabled: selectedRowKeys.length === 0,
    },
    {
      key: 'bulk_activate',
      label: (
        <span onClick={() => handleBulkStatusUpdate(UserStatus.ACTIVE)}>
          <CheckOutlined /> Kích hoạt đã chọn ({selectedRowKeys.length})
        </span>
      ),
      disabled: selectedRowKeys.length === 0,
    },
    {
      key: 'bulk_deactivate',
      label: (
        <span onClick={() => handleBulkStatusUpdate(UserStatus.INACTIVE)}>
          <StopOutlined /> Tạm ngưng đã chọn ({selectedRowKeys.length})
        </span>
      ),
      disabled: selectedRowKeys.length === 0,
    },
  ];

  const columns = [
    {
      title: t('ID'),
      dataIndex: 'id',
      key: 'id',
      width: 80,
      render: (dom: any, record: UserResponse) => (
        <span
          className="text-blue-600 cursor-pointer"
          onClick={() => navigate(`/${MODULE}/${record.id}`)}
        >
          {dom}
        </span>
      ),
    },
    {
      title: t('Thông tin'),
      dataIndex: 'display_name',
      key: 'display_name',
      render: (_: any, record: UserResponse) => (
        <div className="flex items-center gap-3">
          <Avatar size={40} src={record.avatar_url} icon={<UserOutlined />} />
          <div className="flex flex-col">
            <div className="font-medium">{record.display_name}</div>
            <div className="text-gray-500 text-sm">{record.email}</div>
            {record.phone && (
              <div className="text-gray-400 text-xs">{record.phone}</div>
            )}
          </div>
        </div>
      ),
    },
    {
      title: t('Vai trò'),
      key: 'roles',
      width: 200,
      render: (_, record: any) => {
        // Display RBAC roles from API response
        if (record.roles && record.roles.length > 0) {
          return (
            <div>
              <div className="space-y-1">
                {record.roles.slice(0, 2).map((roleData: any, index: number) => {
                  const role = roleMap.get(roleData.role_id);
                  const roleName = role?.display_name || role?.name || `Role #${roleData.role_id}`;
                  
                  return (
                    <div key={index}>
                      <Tag 
                        color={roleData.is_primary ? "blue" : "green"} 
                        size="small"
                        title={`Status: ${roleData.status}, Assigned: ${new Date(roleData.assigned_at).toLocaleDateString()}`}
                      >
                        {roleData.is_primary && <span className="mr-1">★</span>}
                        {roleName}
                      </Tag>
                      {roleData.is_temporary && (
                        <Tag color="orange" size="small">Tạm thời</Tag>
                      )}
                    </div>
                  );
                })}
                {record.roles.length > 2 && (
                  <Tag color="default" size="small">
                    +{record.roles.length - 2} khác
                  </Tag>
                )}
              </div>
            </div>
          );
        }
        
        // Check if user has tenant membership (fallback)
        const primaryMembership = record.tenant_memberships?.find((m: any) => m.is_primary);
        if (primaryMembership) {
          return (
            <div>
              <Tag color="blue" size="small">
                {primaryMembership.status === 'active' ? 'Thành viên' : primaryMembership.status}
              </Tag>
            </div>
          );
        } else if (record.role) {
          // Fallback to old role system  
          return <Tag color="blue" size="small">{record.role}</Tag>;
        }
        
        return (
          <Tag color="default" size="small">Chưa có vai trò</Tag>
        );
      },
    },
    {
      title: t('Trạng thái'),
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: UserStatus) => {
        if (!status) return null;
        const statusMap: Record<UserStatus, { color: string; text: string }> = {
          [UserStatus.ACTIVE]: { color: 'green', text: t('Hoạt động') },
          [UserStatus.INACTIVE]: { color: 'gray', text: t('Không hoạt động') },
          [UserStatus.SUSPENDED]: { color: 'orange', text: t('Tạm ngưng') },
          [UserStatus.PENDING_VERIFICATION]: {
            color: 'blue',
            text: t('Chờ xác thực'),
          },
          [UserStatus.DELETED]: { color: 'red', text: t('Đã xóa') },
        };
        const statusInfo = statusMap[status] || {
          color: 'default',
          text: status,
        };
        return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;
      },
    },
    {
      title: t('Xác thực'),
      key: 'verification',
      width: 120,
      render: (_: any, record: UserResponse) => (
        <div className="flex flex-col gap-1">
          <div className="flex items-center gap-1">
            {record.email_verified ? (
              <CheckCircleOutlined className="text-green-500" />
            ) : (
              <CloseCircleOutlined className="text-red-500" />
            )}
            <span className="text-xs">Email</span>
          </div>
          {record.phone && (
            <div className="flex items-center gap-1">
              {record.phone_verified ? (
                <CheckCircleOutlined className="text-green-500" />
              ) : (
                <CloseCircleOutlined className="text-red-500" />
              )}
              <span className="text-xs">Phone</span>
            </div>
          )}
        </div>
      ),
    },
    {
      title: t('Lần đăng nhập cuối'),
      dataIndex: 'last_login_at',
      key: 'last_login_at',
      width: 150,
      render: (lastLoginAt: string) =>
        lastLoginAt
          ? dayjs(lastLoginAt).format('HH:mm DD/MM/YYYY')
          : 'Chưa đăng nhập',
    },
    {
      title: t('Ngày tạo'),
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (createdAt: string) =>
        createdAt ? dayjs(createdAt).format('HH:mm DD/MM/YYYY') : '',
    },
    {
      title: t('Hành động'),
      dataIndex: '',
      key: 'action',
      width: 120,
      fixed: 'right' as const,
      render: (_: any, record: UserResponse) => (
        <Dropdown.Button
          size="small"
          menu={{
            items: [
              {
                key: 'detail',
                label: (
                  <span onClick={() => handleActions('detail', record)}>
                    <EyeOutlined /> Chi tiết
                  </span>
                ),
              },
              {
                key: 'reset_password',
                label: (
                  <span onClick={() => handleActions('reset_password', record)}>
                    <KeyOutlined /> Reset mật khẩu
                  </span>
                ),
              },
              {
                key: 'toggle_status',
                label: (
                  <span onClick={() => handleActions('toggle_status', record)}>
                    {record.status === UserStatus.ACTIVE ? (
                      <>
                        <StopOutlined /> Tạm ngưng
                      </>
                    ) : (
                      <>
                        <CheckOutlined /> Kích hoạt
                      </>
                    )}
                  </span>
                ),
              },
              {
                key: 'manage_roles',
                label: (
                  <span onClick={() => handleActions('manage_roles', record)}>
                    <SafetyOutlined /> Quản lý vai trò
                  </span>
                ),
              },
              {
                key: 'view_logs',
                label: (
                  <span onClick={() => handleActions('view_logs', record)}>
                    <HistoryOutlined /> Xem nhật ký
                  </span>
                ),
              },
              {
                type: 'divider',
              },
              {
                key: 'delete',
                label: (
                  <Popconfirm
                    placement="top"
                    title={t('deleteConfirm')}
                    onConfirm={() => handleDelete(record.id.toString())}
                    okText="Xóa"
                    cancelText="Hủy"
                  >
                    <span className="text-red-500">
                      <DeleteOutlined /> {t('btnDelete')}
                    </span>
                  </Popconfirm>
                ),
              },
            ],
          }}
          onClick={() => handleActions('edit', record)}
        >
          <EditOutlined /> {t('btnEdit')}
        </Dropdown.Button>
      ),
    },
  ];

  return (
    <div>
      <ListHeader
        title={t('module')}
        module={MODULE}
        showBackButton={true}
        backDestination="dashboard"
        onAddClick={() => handleActions('add', null)}
        addButtonText={t('btnAdd')}
        icon={<PlusCircleOutlined />}
        customActions={
          <Button
            icon={<MailOutlined />}
            onClick={() => setShowInviteModal(true)}
          >
            Mời người dùng
          </Button>
        }
      />

      <div className="bg-gray-50 p-4 rounded-lg mb-4">{/* Status section */}

        {selectedRowKeys.length > 0 && (
          <div className="bg-blue-50 p-3 rounded mb-4">
            <div className="flex justify-between items-center">
              <span className="text-blue-700">
                Đã chọn {selectedRowKeys.length} người dùng
              </span>
              <div className="flex gap-2">
                <Dropdown
                  menu={{ items: bulkActionMenuItems }}
                  placement="bottomRight"
                >
                  <Button>
                    Hành động <DownOutlined />
                  </Button>
                </Dropdown>
                <Button onClick={() => setSelectedRowKeys([])}>
                  Bỏ chọn tất cả
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>

      <Search query={query} loading={loading} onChange={handleFilters} />

      <Space direction="vertical" className="bg-white w-full gap-0">
        <Table
          rowKey="id"
          loading={loading}
          columns={columns}
          dataSource={items}
          pagination={false}
          rowSelection={rowSelection}
          scroll={{ x: 1200 }}
        />
        <Row justify="space-between" align="middle" className="p-4">
          <Col>
            <span className="text-gray-600">
              Hiển thị {items.length} người dùng
            </span>
          </Col>
          <Col>
            <CursorPagination
              total={items.length}
              isNext={isNext}
              isBack={isBack}
              goNext={goNext}
              goBack={goBack}
              showTotal={false}
            />
          </Col>
        </Row>

        {MODULE_POPUP && (
          <ModalForm
            showModal={showModal}
            onChange={handleModal}
            id={idCurrent}
          />
        )}
      </Space>

      {/* Invite User Modal */}
      <InviteUserModal
        visible={showInviteModal}
        onClose={() => setShowInviteModal(false)}
        onSuccess={() => fetchData()}
      />

      {/* Import User Modal */}
      <ImportUserModal
        visible={showImportModal}
        onClose={() => setShowImportModal(false)}
        onSuccess={() => fetchData()}
      />

      {/* User Activity Logs */}
      <UserActivityLogs
        visible={showActivityLogs}
        onClose={() => {
          setShowActivityLogs(false);
          setSelectedUser(null);
        }}
        userId={selectedUser?.id}
        userName={selectedUser?.display_name}
      />

      {/* User Role Manager */}
      <UserRoleManager
        visible={showRoleManager}
        onClose={() => {
          setShowRoleManager(false);
          setSelectedUser(null);
          // Reload the user list to show updated roles
          fetchData(undefined, afterKey);
        }}
        userId={selectedUser?.id}
        userName={selectedUser?.display_name}
        userEmail={selectedUser?.email}
      />
    </div>
  );
}
